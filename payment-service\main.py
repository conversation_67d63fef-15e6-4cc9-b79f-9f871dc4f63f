import pika
import json
import time
import logging
import os
from utils import update_status

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PaymentService:
    def __init__(self):
        self.connection = None
        self.channel = None
        self.payments = {}  # application_id -> payment_info
        self.connect_rabbitmq()
        self.setup_queues()

    def connect_rabbitmq(self):
        rabbitmq_host = os.getenv('RABBITMQ_HOST', 'localhost')
        rabbitmq_user = os.getenv('RABBITMQ_USER', 'admin')
        rabbitmq_pass = os.getenv('RABBITMQ_PASS', 'admin123')

        credentials = pika.PlainCredentials(rabbitmq_user, rabbitmq_pass)

        for i in range(10):
            try:
                self.connection = pika.BlockingConnection(
                    pika.ConnectionParameters(
                        host=rabbitmq_host,
                        credentials=credentials,
                        heartbeat=600,
                        blocked_connection_timeout=300
                    )
                )
                self.channel = self.connection.channel()
                logger.info("✅ Payment Service connected to RabbitMQ")
                return
            except pika.exceptions.AMQPConnectionError:
                logger.warning(f"⏳ Waiting for RabbitMQ... ({i+1}/10)")
                time.sleep(5)
        raise Exception("❌ Could not connect to RabbitMQ")

    def setup_queues(self):
        # Input queues
        self.channel.queue_declare(queue='process_payment', durable=True)
        self.channel.queue_declare(queue='compensate_payment', durable=True)

        # Output queues
        self.channel.queue_declare(queue='payment_processed', durable=True)
        self.channel.queue_declare(queue='payment_failed', durable=True)
        self.channel.queue_declare(queue='payment_compensated', durable=True)

        # Consumers
        self.channel.basic_consume(
            queue='process_payment',
            on_message_callback=self.process_payment,
            auto_ack=False
        )

        self.channel.basic_consume(
            queue='compensate_payment',
            on_message_callback=self.compensate_payment,
            auto_ack=False
        )

        logger.info("💳 Payment Service is listening...")
        self.channel.start_consuming()

    def publish_event(self, queue, data):
        self.channel.basic_publish(
            exchange='',
            routing_key=queue,
            body=json.dumps(data),
            properties=pika.BasicProperties(delivery_mode=2)
        )
        logger.info(f"📤 Event sent to {queue}: {data['application_id']}")

    def process_payment(self, ch, method, properties, body):
        try:
            data = json.loads(body)
            application_id = data["application_id"]
            saga_id = data.get("saga_id", application_id)

            logger.info(f"💰 Processing payment for application {application_id}")

            # Payment işlemi simülasyonu (3 saniye)
            time.sleep(3)

            # Payment amount hesapla (simülasyon)
            amount = 1500.00  # Sabit ücret

            # Check for demo force fail
            force_fail = data.get('force_fail')
            should_fail = False

            if force_fail == 'payment':
                should_fail = True
                error_msg = "🎯 Demo: Forced payment failure - ALWAYS FAILS in demo mode"
            elif time.time() % 7 < 1:  # %15 hata simülasyonu
                should_fail = True
                error_msg = "Credit card declined"

            if should_fail:
                logger.error(f"❌ Payment failed: {application_id}")
                self.publish_event('payment_failed', {
                    'application_id': application_id,
                    'saga_id': saga_id,
                    'error': error_msg
                })
            else:
                # Payment bilgilerini sakla
                self.payments[application_id] = {
                    'amount': amount,
                    'paid_at': time.time(),
                    'transaction_id': f"TXN-{int(time.time())}"
                }

                # Status güncelle
                update_status(application_id, "payment_processed")

                logger.info(f"✅ Payment completed for {application_id} - Amount: ${amount}")
                self.publish_event('payment_processed', {
                    'application_id': application_id,
                    'saga_id': saga_id,
                    'amount': amount
                })

            ch.basic_ack(delivery_tag=method.delivery_tag)

        except Exception as e:
            logger.error(f"❌ Error processing payment: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

    def compensate_payment(self, ch, method, properties, body):
        try:
            data = json.loads(body)
            application_id = data["application_id"]
            saga_id = data.get("saga_id", application_id)

            logger.info(f"💸 Refunding payment for {application_id}")

            # Payment bilgilerini kontrol et
            if application_id in self.payments:
                payment_info = self.payments[application_id]
                amount = payment_info['amount']

                # İade işlemi simülasyonu (2 saniye)
                time.sleep(2)

                # İade bilgilerini güncelle
                payment_info['refunded'] = True
                payment_info['refunded_at'] = time.time()
                payment_info['refund_id'] = f"REF-{int(time.time())}"

                logger.info(f"✅ Payment refunded for {application_id} - Amount: ${amount}")

                # Status güncelle
                update_status(application_id, "payment_refunded")
            else:
                logger.warning(f"⚠️ No payment found to refund for {application_id}")

            # Orchestrator'a bildir
            self.publish_event('payment_compensated', {
                'application_id': application_id,
                'saga_id': saga_id
            })

            ch.basic_ack(delivery_tag=method.delivery_tag)

        except Exception as e:
            logger.error(f"❌ Error compensating payment: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

if __name__ == "__main__":
    PaymentService()
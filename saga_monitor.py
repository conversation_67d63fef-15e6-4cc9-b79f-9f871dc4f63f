#!/usr/bin/env python3
"""
Saga Pattern Monitoring Tool
Real-time monitoring of saga execution and system health
"""

import requests
import time
import json
import sys
from datetime import datetime
from typing import Dict, List, Any

class SagaMonitor:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.last_check = {}
        
    def get_health_status(self) -> Dict[str, Any]:
        """Get system health status"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            return {"status": "error", "error": str(e)}
        return {"status": "unknown"}
    
    def get_all_applications(self) -> Dict[str, Any]:
        """Get all applications with their status"""
        try:
            response = requests.get(f"{self.base_url}/applications", timeout=5)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            return {"error": str(e)}
        return {}
    
    def get_application_logs(self, app_id: str) -> List[Dict[str, Any]]:
        """Get logs for a specific application"""
        try:
            response = requests.get(f"{self.base_url}/applications/{app_id}/logs", timeout=5)
            if response.status_code == 200:
                return response.json().get('logs', [])
        except Exception:
            pass
        return []
    
    def format_timestamp(self, timestamp: float) -> str:
        """Format timestamp for display"""
        return datetime.fromtimestamp(timestamp).strftime('%H:%M:%S')
    
    def get_status_emoji(self, status: str) -> str:
        """Get emoji for status"""
        status_map = {
            'started': '🚀',
            'document_processing': '📄',
            'documents_completed': '✅',
            'payment_processing': '💳',
            'payment_completed': '✅',
            'enrollment_processing': '🎓',
            'enrollment_completed': '✅',
            'notification_processing': '📧',
            'notification_completed': '✅',
            'completed': '🎉',
            'failed': '❌',
            'compensating': '🔄',
            'compensation_completed': '🔄✅'
        }
        return status_map.get(status, '❓')
    
    def display_health_status(self, health: Dict[str, Any]):
        """Display system health status"""
        status = health.get('status', 'unknown')
        rabbitmq = health.get('rabbitmq', 'unknown')
        app_count = health.get('applications_count', 0)
        
        print(f"🏥 System Health: {status}")
        print(f"🐰 RabbitMQ: {rabbitmq}")
        print(f"📊 Active Applications: {app_count}")
    
    def display_applications_summary(self, apps_data: Dict[str, Any]):
        """Display applications summary"""
        if 'error' in apps_data:
            print(f"❌ Error getting applications: {apps_data['error']}")
            return
        
        total = apps_data.get('total', 0)
        applications = apps_data.get('applications', [])
        
        print(f"\n📋 Applications Summary (Total: {total})")
        print("-" * 80)
        
        if not applications:
            print("   No applications found")
            return
        
        # Group by status
        status_groups = {}
        for app in applications:
            status = app.get('saga_status', 'unknown')
            if status not in status_groups:
                status_groups[status] = []
            status_groups[status].append(app)
        
        for status, apps in status_groups.items():
            emoji = self.get_status_emoji(status)
            print(f"\n{emoji} {status.upper()} ({len(apps)} applications)")
            
            for app in apps[:5]:  # Show max 5 per status
                app_id = app.get('application_id', 'Unknown')
                name = app.get('student_name', 'Unknown')
                dept = app.get('department', 'Unknown')
                submitted = app.get('submitted_at', 0)
                time_str = self.format_timestamp(submitted) if submitted else 'Unknown'
                
                print(f"   {app_id}: {name} ({dept}) - {time_str}")
            
            if len(apps) > 5:
                print(f"   ... and {len(apps) - 5} more")
    
    def display_recent_activity(self, apps_data: Dict[str, Any]):
        """Display recent saga activity"""
        applications = apps_data.get('applications', [])
        if not applications:
            return
        
        print(f"\n📈 Recent Activity")
        print("-" * 80)
        
        # Get recent logs from all applications
        all_logs = []
        for app in applications[-10:]:  # Check last 10 applications
            app_id = app.get('application_id')
            if app_id:
                logs = self.get_application_logs(app_id)
                for log in logs[-3:]:  # Last 3 logs per app
                    log['app_id'] = app_id
                    log['student_name'] = app.get('student_name', 'Unknown')
                    all_logs.append(log)
        
        # Sort by timestamp and show recent
        all_logs.sort(key=lambda x: x.get('timestamp', 0), reverse=True)
        
        for log in all_logs[:10]:  # Show 10 most recent
            timestamp = self.format_timestamp(log.get('timestamp', 0))
            app_id = log.get('app_id', 'Unknown')
            student = log.get('student_name', 'Unknown')
            step = log.get('step', 'unknown')
            message = log.get('message', 'No message')
            
            print(f"   {timestamp} | {app_id} ({student})")
            print(f"      {step}: {message}")
    
    def monitor_continuous(self, refresh_interval=5):
        """Continuous monitoring with refresh"""
        print("🔍 Starting Saga Monitor - Press Ctrl+C to stop")
        print("=" * 80)
        
        try:
            while True:
                # Clear screen (works on most terminals)
                print("\033[2J\033[H", end="")
                
                print(f"🕐 Last Update: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print("=" * 80)
                
                # Health check
                health = self.get_health_status()
                self.display_health_status(health)
                
                # Applications summary
                apps_data = self.get_all_applications()
                self.display_applications_summary(apps_data)
                
                # Recent activity
                self.display_recent_activity(apps_data)
                
                print(f"\n🔄 Refreshing in {refresh_interval} seconds... (Ctrl+C to stop)")
                time.sleep(refresh_interval)
                
        except KeyboardInterrupt:
            print("\n\n👋 Monitoring stopped by user")
        except Exception as e:
            print(f"\n❌ Monitoring error: {e}")
    
    def monitor_single_application(self, app_id: str, max_duration=60):
        """Monitor a specific application"""
        print(f"🔍 Monitoring Application: {app_id}")
        print("=" * 60)
        
        start_time = time.time()
        last_status = None
        
        try:
            while time.time() - start_time < max_duration:
                try:
                    # Get application status
                    response = requests.get(f"{self.base_url}/applications/{app_id}")
                    if response.status_code == 200:
                        app_data = response.json()
                        current_status = app_data.get('saga_status', 'unknown')
                        
                        # Only print if status changed
                        if current_status != last_status:
                            timestamp = datetime.now().strftime('%H:%M:%S')
                            emoji = self.get_status_emoji(current_status)
                            print(f"{timestamp} | {emoji} Status: {current_status}")
                            
                            # Get recent logs
                            logs = self.get_application_logs(app_id)
                            if logs:
                                latest_log = logs[-1]
                                log_time = self.format_timestamp(latest_log.get('timestamp', 0))
                                step = latest_log.get('step', 'unknown')
                                message = latest_log.get('message', 'No message')
                                print(f"         | 📝 {log_time} - {step}: {message}")
                            
                            last_status = current_status
                            
                            # Check if saga is finished
                            if current_status in ['completed', 'failed', 'compensation_completed']:
                                print(f"\n🏁 Saga finished with status: {current_status}")
                                break
                    else:
                        print(f"❌ Failed to get application status: {response.status_code}")
                        break
                        
                except Exception as e:
                    print(f"❌ Error monitoring application: {e}")
                    break
                
                time.sleep(2)
            
            if time.time() - start_time >= max_duration:
                print(f"\n⏰ Monitoring timeout after {max_duration} seconds")
                
        except KeyboardInterrupt:
            print("\n👋 Monitoring stopped by user")

def main():
    """Main function with command line interface"""
    monitor = SagaMonitor()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "health":
            health = monitor.get_health_status()
            monitor.display_health_status(health)
            
        elif command == "apps":
            apps_data = monitor.get_all_applications()
            monitor.display_applications_summary(apps_data)
            
        elif command == "watch" and len(sys.argv) > 2:
            app_id = sys.argv[2]
            monitor.monitor_single_application(app_id)
            
        elif command == "continuous":
            interval = int(sys.argv[2]) if len(sys.argv) > 2 else 5
            monitor.monitor_continuous(interval)
            
        else:
            print("Usage:")
            print("  python saga_monitor.py health              - Check system health")
            print("  python saga_monitor.py apps                - Show applications summary")
            print("  python saga_monitor.py watch <app_id>      - Monitor specific application")
            print("  python saga_monitor.py continuous [interval] - Continuous monitoring")
    else:
        # Default: continuous monitoring
        monitor.monitor_continuous()

if __name__ == "__main__":
    main()

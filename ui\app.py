import streamlit as st
import requests
import time
from datetime import datetime
import json
import os

st.set_page_config(
    page_title="🎓 University Saga Application System",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

STATUS_FILE = "status.json"

def format_ts(ts):
    return datetime.fromtimestamp(ts).strftime("%Y-%m-%d %H:%M:%S")

def get_status_icon(status):
    icons = {
        "submitted": "🟡",
        "started": "🔠",
        "processing": "🔠",
        "completed": "🟢",
        "failed": "🔴",
        "compensating": "🔣",
    }
    return icons.get(status, "⚫")

def submit_app(name, email, department, phone):
    try:
        r = requests.post("http://application-service:8000/applications", json={
            "student_name": name,
            "email": email,
            "department": department,
            "phone": phone
        }, timeout=5)
        r.raise_for_status()
        return r.json()
    except Exception as e:
        st.error(f"❌ Submission error: {e}")
        return None

def fetch_status(app_id):
    try:
        r = requests.get(f"http://application-service:8000/applications/{app_id}", timeout=5)
        r.raise_for_status()
        return r.json()
    except:
        return None

def fetch_all():
    try:
        r = requests.get("http://application-service:8000/applications", timeout=5)
        r.raise_for_status()
        return r.json()
    except:
        return None

def fetch_logs(app_id):
    try:
        r = requests.get(f"http://application-service:8000/applications/{app_id}/logs", timeout=5)
        r.raise_for_status()
        return r.json().get("logs", [])
    except:
        return []

def load_status():
    if os.path.exists(STATUS_FILE):
        with open(STATUS_FILE, "r") as f:
            return json.load(f)
    return {}

def save_status(status):
    with open(STATUS_FILE, "w") as f:
        json.dump(status, f, indent=4)

def update_status(app_id, step):
    status = load_status()
    if app_id not in status:
        status[app_id] = {}
    status[app_id][step] = True
    save_status(status)

st.title("🎓 University Saga Application System")

menu = st.sidebar.radio("📋 Menu", ["Submit", "Track", "All", "About"])

if menu == "Submit":
    st.subheader("📝 Submit New Application")
    with st.form("submit_form"):
        name = st.text_input("👤 Full Name")
        email = st.text_input("📧 Email")
        department = st.selectbox("🏫 Department", [
            "Computer Science", "Mathematics", "Physics", "Biology", "Business"
        ])
        phone = st.text_input("📱 Phone (optional)")
        submit = st.form_submit_button("🚀 Submit")

        if submit and name and email:
            with st.spinner("Sending..."):
                result = submit_app(name, email, department, phone)
            if result:
                st.session_state['last_result'] = result

    if 'last_result' in st.session_state:
        result = st.session_state['last_result']
        st.success("✅ Application Submitted!")
        st.markdown(f"**Application ID:** `{result['application_id']}`")
        st.markdown(f"**Message:** {result['message']}`")
        st.markdown(f"**Status:** {result['status']}`")
        st.markdown(f"**Timestamp:** {format_ts(result['timestamp'])}`")

elif menu == "Track":
    st.subheader("🔍 Track Application")
    app_id = st.text_input("🔢 Application ID")
    refresh = st.button("🔄 Refresh")

    if app_id and refresh:
        data = fetch_status(app_id)
        logs = fetch_logs(app_id)
        if data:
            st.markdown(f"**ID:** {data['application_id']}")
            st.markdown(f"**Name:** {data['student_name']}")
            st.markdown(f"**Email:** {data['email']}")
            st.markdown(f"**Department:** {data['department']}")
            st.markdown(f"**Submitted:** {format_ts(data['submitted_at'])}")
            st.markdown(f"**Status:** {get_status_icon(data['status'])} {data['status']}")
            st.markdown(f"**Saga:** {get_status_icon(data['saga_status'])} {data['saga_status']}")
            if 'amount' in data:
                st.markdown(f"**💳 Payment Amount:** ${data['amount']}")
            if 'paid_at' in data:
                st.markdown(f"**🕒 Payment Time:** {format_ts(data['paid_at'])}")
            if logs:
                st.subheader("📜 Saga Event Log")
                for entry in logs:
                    st.markdown(f"- `{format_ts(entry['timestamp'])}` → **{entry['step']}**: {entry['message']}")
        else:
            st.error("❌ Not found")

elif menu == "All":
    st.subheader("📊 All Applications")
    data = fetch_all()
    if data:
        for app in data["applications"]:
            with st.expander(f"{app['application_id']} - {app['student_name']}"):
                st.markdown(f"**📧** {app['email']}")
                st.markdown(f"**🏫** {app['department']}")
                st.markdown(f"**📱** {app.get('phone', 'N/A')}")
                st.markdown(f"**⏰** {format_ts(app['submitted_at'])}")
                st.markdown(f"**Status:** {get_status_icon(app['status'])} {app['status']}")
                st.markdown(f"**Saga:** {get_status_icon(app['saga_status'])} {app['saga_status']}")
                if 'amount' in app:
                    st.markdown(f"**💳 Payment Amount:** ${app['amount']}")
                if 'paid_at' in app:
                    st.markdown(f"**🕒 Payment Time:** {format_ts(app['paid_at'])}")
    else:
        st.warning("⚠️ No data found.")

elif menu == "About":
    st.subheader("ℹ️ About This System")
    st.markdown("""
    This system is a Saga Pattern-based microservices demo project built by Rabia Çevik as part of her master's thesis at Istanbul Kültür University, Department of Computer Engineering.

    - 🐇 RabbitMQ for messaging
    - ⚙️ FastAPI for backend services
    - ♻️ Saga orchestration for distributed transaction handling
    - 🎨 Streamlit for interactive UI
    - 💳 Simulated payment transaction logic
    - 📧 Notification and rollback simulations
    - 📜 Live log tracking of each Saga step per application
    """)

import streamlit as st
import requests
import time
from datetime import datetime
import json
import os

st.set_page_config(
    page_title="🎓 University Saga Demo",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

STATUS_FILE = "status.json"

def format_ts(ts):
    return datetime.fromtimestamp(ts).strftime("%Y-%m-%d %H:%M:%S")

def get_status_icon(status):
    icons = {
        "submitted": "🟡",
        "started": "🚀",
        "document_processing": "📄",
        "documents_completed": "✅",
        "payment_processing": "💳",
        "payment_completed": "✅",
        "enrollment_processing": "🎓",
        "enrollment_completed": "✅",
        "notification_processing": "📧",
        "notification_completed": "✅",
        "completed": "🎉",
        "failed": "❌",
        "compensating": "🔄",
        "compensation_completed": "🔄✅",
        "processing": "⚙️",
    }
    return icons.get(status, "⚫")

def get_saga_steps():
    """Define saga steps for visualization"""
    return [
        {"name": "Application", "icon": "📝", "color": "#FF6B6B"},
        {"name": "Documents", "icon": "📄", "color": "#4ECDC4"},
        {"name": "Payment", "icon": "💳", "color": "#45B7D1"},
        {"name": "Enrollment", "icon": "🎓", "color": "#96CEB4"},
        {"name": "Notification", "icon": "📧", "color": "#FFEAA7"}
    ]

def create_simple_saga_flow(app_data, logs):
    """Create a simple, clear saga flow visualization"""
    saga_status = app_data.get('saga_status', 'unknown')

    # Parse what happened from logs
    completed_steps = []
    failed_step = None
    compensated_steps = []

    for log in logs:
        step = log.get('step', '')
        message = log.get('message', '')

        if 'completed successfully' in message or 'processed' in message:
            if 'document' in step:
                completed_steps.append('Documents')
            elif 'payment' in step:
                completed_steps.append('Payment')
            elif 'enrollment' in step:
                completed_steps.append('Enrollment')
            elif 'notification' in step:
                completed_steps.append('Notification')
        elif 'failed' in message:
            if 'payment' in step:
                failed_step = 'Payment'
            elif 'notification' in step:
                failed_step = 'Notification'
        elif 'compensat' in message:
            if 'document' in step:
                compensated_steps.append('Documents')
            elif 'payment' in step:
                compensated_steps.append('Payment')
            elif 'enrollment' in step:
                compensated_steps.append('Enrollment')

    # Create big, clear visual
    st.markdown("## 🔄 Saga Journey")

    # Step 1: Application
    st.markdown("""
    <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 10px 0; border-left: 5px solid #4CAF50;">
        <h3>📝 Step 1: Application Submitted</h3>
        <p><strong>✅ COMPLETED</strong> - Student information received</p>
    </div>
    """, unsafe_allow_html=True)

    # Step 2: Documents
    if 'Documents' in completed_steps:
        if 'Documents' in compensated_steps:
            bg_color = "#fff3cd"
            border_color = "#ffc107"
            status = "🔄 COMPENSATED - Documents were removed"
        else:
            bg_color = "#e8f5e8"
            border_color = "#4CAF50"
            status = "✅ COMPLETED - Documents processed successfully"
    else:
        bg_color = "#f8f9fa"
        border_color = "#6c757d"
        status = "⚫ PENDING - Waiting to process"

    st.markdown(f"""
    <div style="background: {bg_color}; padding: 20px; border-radius: 10px; margin: 10px 0; border-left: 5px solid {border_color};">
        <h3>📄 Step 2: Document Processing</h3>
        <p><strong>{status}</strong></p>
    </div>
    """, unsafe_allow_html=True)

    # Step 3: Payment
    if 'Payment' in completed_steps:
        if 'Payment' in compensated_steps:
            bg_color = "#fff3cd"
            border_color = "#ffc107"
            status = "🔄 COMPENSATED - Payment was refunded"
        else:
            bg_color = "#e8f5e8"
            border_color = "#4CAF50"
            status = "✅ COMPLETED - Payment processed successfully"
    elif failed_step == 'Payment':
        bg_color = "#f8d7da"
        border_color = "#dc3545"
        status = "❌ FAILED - Credit card was declined"
    else:
        bg_color = "#f8f9fa"
        border_color = "#6c757d"
        status = "⚫ PENDING - Waiting for payment"

    st.markdown(f"""
    <div style="background: {bg_color}; padding: 20px; border-radius: 10px; margin: 10px 0; border-left: 5px solid {border_color};">
        <h3>💳 Step 3: Payment Processing</h3>
        <p><strong>{status}</strong></p>
    </div>
    """, unsafe_allow_html=True)

    # Step 4: Enrollment
    if 'Enrollment' in completed_steps:
        if 'Enrollment' in compensated_steps:
            bg_color = "#fff3cd"
            border_color = "#ffc107"
            status = "🔄 COMPENSATED - Enrollment was cancelled"
        else:
            bg_color = "#e8f5e8"
            border_color = "#4CAF50"
            status = "✅ COMPLETED - Student enrolled successfully"
    else:
        bg_color = "#f8f9fa"
        border_color = "#6c757d"
        status = "⚫ PENDING - Waiting for enrollment"

    st.markdown(f"""
    <div style="background: {bg_color}; padding: 20px; border-radius: 10px; margin: 10px 0; border-left: 5px solid {border_color};">
        <h3>🎓 Step 4: Enrollment Processing</h3>
        <p><strong>{status}</strong></p>
    </div>
    """, unsafe_allow_html=True)

    # Step 5: Notification
    if 'Notification' in completed_steps:
        bg_color = "#e8f5e8"
        border_color = "#4CAF50"
        status = "✅ COMPLETED - Confirmation email sent"
    elif failed_step == 'Notification':
        bg_color = "#f8d7da"
        border_color = "#dc3545"
        status = "❌ FAILED - Email service timeout"
    else:
        bg_color = "#f8f9fa"
        border_color = "#6c757d"
        status = "⚫ PENDING - Waiting to send email"

    st.markdown(f"""
    <div style="background: {bg_color}; padding: 20px; border-radius: 10px; margin: 10px 0; border-left: 5px solid {border_color};">
        <h3>📧 Step 5: Notification</h3>
        <p><strong>{status}</strong></p>
    </div>
    """, unsafe_allow_html=True)

    # Final result
    if saga_status == 'completed':
        st.success("🎉 **SAGA COMPLETED SUCCESSFULLY!** All steps finished without errors.")
    elif saga_status in ['failed', 'compensation_completed']:
        st.error("❌ **SAGA FAILED BUT SYSTEM IS CONSISTENT!** All completed steps were properly rolled back.")
    else:
        st.info("⚙️ **SAGA IN PROGRESS...** Please wait for all steps to complete.")

def submit_app(name, email, department, phone):
    try:
        r = requests.post("http://application-service:8000/applications", json={
            "student_name": name,
            "email": email,
            "department": department,
            "phone": phone
        }, timeout=5)
        r.raise_for_status()
        return r.json()
    except Exception as e:
        st.error(f"❌ Submission error: {e}")
        return None

def fetch_status(app_id):
    try:
        r = requests.get(f"http://application-service:8000/applications/{app_id}", timeout=5)
        r.raise_for_status()
        return r.json()
    except:
        return None

def fetch_all():
    try:
        r = requests.get("http://application-service:8000/applications", timeout=5)
        r.raise_for_status()
        return r.json()
    except:
        return None

def fetch_logs(app_id):
    try:
        r = requests.get(f"http://application-service:8000/applications/{app_id}/logs", timeout=5)
        r.raise_for_status()
        return r.json().get("logs", [])
    except:
        return []

def load_status():
    if os.path.exists(STATUS_FILE):
        with open(STATUS_FILE, "r") as f:
            return json.load(f)
    return {}

def save_status(status):
    with open(STATUS_FILE, "w") as f:
        json.dump(status, f, indent=4)

def update_status(app_id, step):
    status = load_status()
    if app_id not in status:
        status[app_id] = {}
    status[app_id][step] = True
    save_status(status)

def create_simple_statistics(data):
    """Create simple statistics without complex charts"""
    if not data or 'applications' not in data:
        st.warning("No data available for statistics")
        return

    apps = data['applications']

    # Status distribution
    status_counts = {}
    department_counts = {}

    for app in apps:
        status = app.get('saga_status', 'unknown')
        dept = app.get('department', 'Unknown')

        status_counts[status] = status_counts.get(status, 0) + 1
        department_counts[dept] = department_counts.get(dept, 0) + 1

    # Create metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_apps = len(apps)
        st.metric("📊 Total Applications", total_apps)

    with col2:
        completed = status_counts.get('completed', 0)
        success_rate = (completed / total_apps * 100) if total_apps > 0 else 0
        st.metric("✅ Success Rate", f"{success_rate:.1f}%")

    with col3:
        failed = status_counts.get('failed', 0) + status_counts.get('compensation_completed', 0)
        failure_rate = (failed / total_apps * 100) if total_apps > 0 else 0
        st.metric("❌ Failure Rate", f"{failure_rate:.1f}%")

    with col4:
        processing = sum(1 for app in apps if app.get('saga_status', '') not in ['completed', 'failed', 'compensation_completed'])
        st.metric("⚙️ Processing", processing)

    # Simple text-based distribution
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📈 Status Distribution")
        for status, count in status_counts.items():
            percentage = (count / total_apps * 100) if total_apps > 0 else 0
            icon = get_status_icon(status)
            st.write(f"{icon} **{status}**: {count} ({percentage:.1f}%)")

    with col2:
        st.subheader("🏫 Department Distribution")
        for dept, count in department_counts.items():
            percentage = (count / total_apps * 100) if total_apps > 0 else 0
            st.write(f"🏫 **{dept}**: {count} ({percentage:.1f}%)")

def create_real_time_monitor():
    """Create real-time monitoring section"""
    st.subheader("🔴 Live Saga Monitor")

    # Auto-refresh toggle
    auto_refresh = st.checkbox("🔄 Auto-refresh (5 seconds)", value=False)

    if auto_refresh:
        time.sleep(5)
        st.rerun()

    # Manual refresh button
    if st.button("🔄 Refresh Now"):
        st.rerun()

    # Get latest data
    data = fetch_all()
    if data and 'applications' in data:
        apps = data['applications']

        # Show recent applications
        st.subheader("📋 Recent Applications")

        # Sort by submission time (most recent first)
        recent_apps = sorted(apps, key=lambda x: x.get('submitted_at', 0), reverse=True)[:5]

        for app in recent_apps:
            with st.expander(f"{app['application_id']} - {app['student_name']} ({app.get('saga_status', 'unknown')})"):
                # Create saga flow for this application
                logs = fetch_logs(app['application_id'])
                create_simple_saga_flow(app, logs)

                # Show recent logs
                if logs:
                    st.subheader("📜 Recent Logs")
                    for log in logs[-3:]:
                        timestamp = format_ts(log['timestamp'])
                        step = log['step']
                        message = log['message']
                        st.markdown(f"**{timestamp}** - {step}: {message}")

def get_health_status():
    """Get system health status"""
    try:
        r = requests.get("http://application-service:8000/health", timeout=5)
        if r.status_code == 200:
            return r.json()
    except:
        pass
    return {"status": "error", "rabbitmq": "disconnected", "applications_count": 0}

st.title("🎓 Saga Pattern Demo: University Application System")

# Add explanation at the top
st.markdown("""
### 🔄 What is Saga Pattern?
**Problem**: In microservices, one business transaction spans multiple services. What if one step fails?

**Solution**: Define compensation (rollback) for each step. If any step fails, automatically undo previous steps!

**This Demo**: University application with 5 steps. Watch how the system handles failures! 🎯
""")

st.markdown("---")

# Sidebar with system health
st.sidebar.markdown("## 🏥 System Health")
health = get_health_status()
status_color = "🟢" if health.get('status') == 'ok' else "🔴"
rabbitmq_color = "🟢" if health.get('rabbitmq') == 'connected' else "🔴"

st.sidebar.markdown(f"""
- **System**: {status_color} {health.get('status', 'unknown')}
- **RabbitMQ**: {rabbitmq_color} {health.get('rabbitmq', 'unknown')}
- **Applications**: {health.get('applications_count', 0)}
""")

menu = st.sidebar.radio("📋 Menu", ["🏠 Dashboard", "📝 Submit", "🔍 Track", "📊 All Apps", "🔴 Live Monitor", "ℹ️ About"])

if menu == "🏠 Dashboard":
    st.subheader("📊 Saga Pattern Dashboard")

    # Get all applications data
    data = fetch_all()

    if data:
        # Statistics dashboard
        create_simple_statistics(data)

        st.markdown("---")

        # Recent activity
        st.subheader("📈 Recent Saga Activity")
        apps = data.get('applications', [])
        if apps:
            recent_apps = sorted(apps, key=lambda x: x.get('submitted_at', 0), reverse=True)[:3]

            for app in recent_apps:
                with st.expander(f"🔍 {app['application_id']} - {app['student_name']}"):
                    logs = fetch_logs(app['application_id'])
                    st.markdown("### 🔄 Saga Flow")
                    create_simple_saga_flow(app, logs)
        else:
            st.info("No applications found. Submit a new application to see saga flows!")
    else:
        st.warning("Unable to fetch data. Please check if the application service is running.")

elif menu == "📝 Submit":
    st.subheader("📝 Submit New Application")

    # Big warning about simulation
    st.warning("⚠️ **This is a SIMULATION!** No real payment or documents required. The system randomly simulates success/failure to demonstrate Saga Pattern.")

    # Saga pattern explanation
    st.info("""
    **🔄 Saga Steps:**
    1. **📝 Application** - Student info received ✅ (Always succeeds)
    2. **📄 Documents** - Process documents ✅ (Always succeeds)
    3. **💳 Payment** - Process fee 🎲 (85% success, 15% random failure)
    4. **🎓 Enrollment** - Register student ✅ (Usually succeeds)
    5. **📧 Notification** - Send email 🎲 (Sometimes fails)

    **If any step fails → Automatic compensation (rollback) of previous steps!**
    """)

    with st.form("submit_form"):
        name = st.text_input("👤 Full Name")
        email = st.text_input("📧 Email")
        department = st.selectbox("🏫 Department", [
            "Computer Science", "Mathematics", "Physics", "Biology", "Business"
        ])
        phone = st.text_input("📱 Phone (optional)")
        submit = st.form_submit_button("🚀 Submit Application")

        if submit and name and email:
            with st.spinner("🚀 Submitting application and starting saga..."):
                result = submit_app(name, email, department, phone)
            if result:
                st.session_state['last_result'] = result

    if 'last_result' in st.session_state:
        result = st.session_state['last_result']
        st.success("✅ Application Submitted Successfully!")

        col1, col2 = st.columns(2)
        with col1:
            st.markdown(f"**🆔 Application ID:** `{result['application_id']}`")
            st.markdown(f"**📧 Message:** {result['message']}")
            st.markdown(f"**📊 Status:** {result['status']}")
            st.markdown(f"**🕐 Timestamp:** {format_ts(result['timestamp'])}")

        with col2:
            st.info("🔍 **Track your application** using the Application ID in the 'Track' section!")
            if st.button("🔍 Track This Application"):
                st.session_state['track_id'] = result['application_id']
                st.rerun()

elif menu == "🔍 Track":
    st.subheader("🔍 Track Application Saga")

    st.info("💡 **Demo Tip**: Try these Application IDs from the logs: `APP-285903` (successful) or `APP-285871` (failed with compensation)")

    # Check if we have a tracked ID from session state
    default_id = st.session_state.get('track_id', '')
    app_id = st.text_input("🔢 Application ID", value=default_id)

    col1, col2 = st.columns([1, 1])
    with col1:
        refresh = st.button("🔄 Refresh Status")
    with col2:
        auto_refresh = st.checkbox("🔄 Auto-refresh (3 seconds)")

    if auto_refresh:
        time.sleep(3)
        st.rerun()

    if app_id:
        data = fetch_status(app_id)
        logs = fetch_logs(app_id)

        if data:
            # Application info
            col1, col2 = st.columns(2)
            with col1:
                st.markdown("### 👤 Student Information")
                st.markdown(f"**🆔 ID:** {data['application_id']}")
                st.markdown(f"**👤 Name:** {data['student_name']}")
                st.markdown(f"**📧 Email:** {data['email']}")
                st.markdown(f"**🏫 Department:** {data['department']}")
                st.markdown(f"**📱 Phone:** {data.get('phone', 'N/A')}")

            with col2:
                st.markdown("### 📊 Status Information")
                st.markdown(f"**⏰ Submitted:** {format_ts(data['submitted_at'])}")
                st.markdown(f"**📊 Status:** {get_status_icon(data['status'])} {data['status']}")
                st.markdown(f"**🔄 Saga Status:** {get_status_icon(data['saga_status'])} {data['saga_status']}")
                if 'amount' in data:
                    st.markdown(f"**💳 Amount:** ${data['amount']}")

            st.markdown("---")

            # Saga flow visualization
            st.markdown("### 🔄 Saga Flow Visualization")
            create_simple_saga_flow(data, logs)

            st.markdown("---")

            # Detailed logs
            if logs:
                st.markdown("### 📜 Detailed Saga Event Log")

                # Create a timeline view
                for i, entry in enumerate(logs):
                    timestamp = format_ts(entry['timestamp'])
                    step = entry['step']
                    message = entry['message']

                    # Determine icon based on message content
                    if 'completed' in message or 'processed' in message:
                        icon = "✅"
                        color = "green"
                    elif 'failed' in message or 'error' in message:
                        icon = "❌"
                        color = "red"
                    elif 'compensat' in message:
                        icon = "🔄"
                        color = "orange"
                    else:
                        icon = "ℹ️"
                        color = "blue"

                    st.markdown(f"""
                    <div style="border-left: 3px solid {color}; padding-left: 10px; margin: 10px 0;">
                        <strong>{icon} {timestamp}</strong><br>
                        <strong>Step:</strong> {step}<br>
                        <strong>Message:</strong> {message}
                    </div>
                    """, unsafe_allow_html=True)
            else:
                st.info("No logs available yet. The saga may still be starting.")
        else:
            st.error("❌ Application not found. Please check the Application ID.")

elif menu == "📊 All Apps":
    st.subheader("📊 All Applications Overview")
    data = fetch_all()

    if data:
        # Statistics at the top
        create_simple_statistics(data)

        st.markdown("---")

        # Filter options
        col1, col2, col3 = st.columns(3)
        with col1:
            status_filter = st.selectbox("Filter by Status",
                                       ["All"] + list(set(app.get('saga_status', 'unknown') for app in data['applications'])))
        with col2:
            dept_filter = st.selectbox("Filter by Department",
                                     ["All"] + list(set(app.get('department', 'Unknown') for app in data['applications'])))
        with col3:
            sort_by = st.selectbox("Sort by", ["Newest First", "Oldest First", "Name A-Z", "Name Z-A"])

        # Apply filters
        filtered_apps = data["applications"]
        if status_filter != "All":
            filtered_apps = [app for app in filtered_apps if app.get('saga_status') == status_filter]
        if dept_filter != "All":
            filtered_apps = [app for app in filtered_apps if app.get('department') == dept_filter]

        # Apply sorting
        if sort_by == "Newest First":
            filtered_apps = sorted(filtered_apps, key=lambda x: x.get('submitted_at', 0), reverse=True)
        elif sort_by == "Oldest First":
            filtered_apps = sorted(filtered_apps, key=lambda x: x.get('submitted_at', 0))
        elif sort_by == "Name A-Z":
            filtered_apps = sorted(filtered_apps, key=lambda x: x.get('student_name', ''))
        elif sort_by == "Name Z-A":
            filtered_apps = sorted(filtered_apps, key=lambda x: x.get('student_name', ''), reverse=True)

        st.markdown(f"### 📋 Applications ({len(filtered_apps)} found)")

        for app in filtered_apps:
            saga_status = app.get('saga_status', 'unknown')
            status_icon = get_status_icon(saga_status)

            with st.expander(f"{status_icon} {app['application_id']} - {app['student_name']} ({saga_status})"):
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("**👤 Student Info:**")
                    st.markdown(f"- **Name:** {app['student_name']}")
                    st.markdown(f"- **Email:** {app['email']}")
                    st.markdown(f"- **Department:** {app['department']}")
                    st.markdown(f"- **Phone:** {app.get('phone', 'N/A')}")

                with col2:
                    st.markdown("**📊 Application Info:**")
                    st.markdown(f"- **Submitted:** {format_ts(app['submitted_at'])}")
                    st.markdown(f"- **Status:** {get_status_icon(app['status'])} {app['status']}")
                    st.markdown(f"- **Saga Status:** {status_icon} {saga_status}")
                    if 'amount' in app:
                        st.markdown(f"- **Amount:** ${app['amount']}")

                # Mini saga flow
                logs = fetch_logs(app['application_id'])
                if logs:
                    st.markdown("**🔄 Saga Flow:**")
                    create_simple_saga_flow(app, logs)
    else:
        st.warning("⚠️ No applications found.")

elif menu == "🔴 Live Monitor":
    create_real_time_monitor()

elif menu == "ℹ️ About":
    st.subheader("ℹ️ About This Saga Pattern System")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        ### 🎓 Project Information
        This system demonstrates the **Saga Pattern** for managing distributed transactions
        in a university application system.

        **Built by:** Rabia Çevik
        **Institution:** Istanbul Kültür University
        **Department:** Computer Engineering
        **Project:** Master's Thesis Demo
        """)

        st.markdown("""
        ### 🏗️ Architecture
        - **🐇 RabbitMQ** - Message broker for async communication
        - **⚙️ FastAPI** - Backend microservices
        - **🔄 Saga Orchestrator** - Central transaction coordinator
        - **🎨 Streamlit** - Interactive web UI
        - **🐳 Docker** - Containerized deployment
        """)

    with col2:
        st.markdown("""
        ### 🔄 Saga Pattern Benefits

        **Problem Solved:**
        - Distributed transaction management
        - Data consistency across microservices
        - Failure handling and compensation

        **Key Features:**
        - ✅ Automatic compensation on failures
        - 🔄 Retry logic for transient errors
        - 📊 Real-time monitoring and visualization
        - 📜 Complete audit trail
        - 🎯 Orchestrator pattern implementation
        """)

        st.markdown("""
        ### 🚀 Demo Scenarios
        1. **Happy Path** - All steps complete successfully
        2. **Payment Failure** - Automatic compensation
        3. **Multiple Applications** - Concurrent saga execution
        4. **Real-time Monitoring** - Live saga tracking
        """)

    st.markdown("---")

    # Saga flow explanation
    st.markdown("### 🔄 University Application Saga Flow")

    steps = get_saga_steps()
    cols = st.columns(len(steps))

    for step, col in zip(steps, cols):
        with col:
            st.markdown(f"""
            <div style="text-align: center; padding: 15px; border-radius: 10px;
                        background-color: {step['color']}20; border: 2px solid {step['color']};">
                <div style="font-size: 3em;">{step['icon']}</div>
                <div style="font-weight: bold; font-size: 1.2em;">{step['name']}</div>
            </div>
            """, unsafe_allow_html=True)

    st.markdown("""
    ### 💡 How It Works

    1. **Student submits application** → Saga starts
    2. **Each step processes sequentially** → Documents → Payment → Enrollment → Notification
    3. **If any step fails** → Compensation starts in reverse order
    4. **System maintains consistency** → Either all steps complete or all are rolled back

    This ensures **ACID properties** in a distributed microservices environment!
    """)

import json
import os

STATUS_FILE = "status.json"

def load_status():
    if os.path.exists(STATUS_FILE):
        with open(STATUS_FILE, "r") as f:
            return json.load(f)
    return {}

def save_status(status):
    with open(STATUS_FILE, "w") as f:
        json.dump(status, f, indent=4)

def update_status(app_id, step):
    status = load_status()
    if app_id not in status:
        status[app_id] = {}
    status[app_id][step] = True
    save_status(status)

from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
import pika
import json
import time
import logging
import os
from typing import Dict, Any, Optional, List
from utils import update_status

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="University Application Service")

# Global saga logs storage
saga_logs: Dict[str, List[Dict]] = {}

class ApplicationRequest(BaseModel):
    student_name: str
    email: str
    department: str
    phone: Optional[str] = None
    force_fail: Optional[str] = None  # Demo: force failure at specific step

class ApplicationResponse(BaseModel):
    application_id: str
    status: str
    message: str
    timestamp: float

def add_saga_log(application_id: str, step: str, message: str):
    """Add a log entry for saga tracking"""
    if application_id not in saga_logs:
        saga_logs[application_id] = []

    saga_logs[application_id].append({
        'timestamp': time.time(),
        'step': step,
        'message': message
    })
    logger.info(f"📝 Saga log added for {application_id}: {step}")

class ApplicationService:
    def __init__(self):
        self.connection = None
        self.channel = None
        self.applications: Dict[str, Dict] = {}
        self.connect_rabbitmq()
        self.setup_queues()
        self.setup_event_listener()

    def connect_rabbitmq(self):
        rabbitmq_host = os.getenv('RABBITMQ_HOST', 'localhost')
        rabbitmq_user = os.getenv('RABBITMQ_USER', 'admin')
        rabbitmq_pass = os.getenv('RABBITMQ_PASS', 'admin123')

        credentials = pika.PlainCredentials(rabbitmq_user, rabbitmq_pass)

        for i in range(10):
            try:
                self.connection = pika.BlockingConnection(
                    pika.ConnectionParameters(
                        host=rabbitmq_host,
                        credentials=credentials,
                        heartbeat=0,
                        blocked_connection_timeout=None,
                        connection_attempts=3,
                        retry_delay=2
                    )
                )
                self.channel = self.connection.channel()
                logger.info("✅ Application Service connected to RabbitMQ")
                return
            except Exception as e:
                logger.warning(f"⏳ Waiting for RabbitMQ... ({i+1}/10) - {e}")
                time.sleep(5)
        raise Exception("❌ Failed to connect to RabbitMQ")

    def setup_queues(self):
        try:
            self.channel.queue_declare(queue='application_submitted', durable=True)
            logger.info("✅ Queue 'application_submitted' declared")
        except Exception as e:
            logger.error(f"❌ Failed to setup queues: {e}")

    def setup_event_listener(self):
        """Listen for saga events to update application status"""
        try:
            # Create a separate channel for consuming
            self.consumer_channel = self.connection.channel()

            # Declare event queues we want to listen to
            event_queues = [
                'document_processed', 'document_failed',
                'payment_processed', 'payment_failed',
                'enrollment_processed', 'enrollment_failed',
                'notification_sent', 'notification_failed'
            ]

            for queue in event_queues:
                self.consumer_channel.queue_declare(queue=queue, durable=True)

            # Start consuming in a separate thread
            import threading
            consumer_thread = threading.Thread(target=self._consume_events)
            consumer_thread.daemon = True
            consumer_thread.start()

        except Exception as e:
            logger.error(f"❌ Failed to setup event listener: {e}")

    def _consume_events(self):
        """Background consumer for saga events"""
        def handle_event(ch, method, properties, body):
            try:
                data = json.loads(body)
                app_id = data.get('application_id')
                queue_name = method.routing_key

                # Update application status based on event
                if app_id in self.applications:
                    if 'processed' in queue_name or 'sent' in queue_name:
                        step = queue_name.replace('_processed', '').replace('_sent', '')
                        self.applications[app_id]['saga_status'] = f"{step}_completed"
                        add_saga_log(app_id, step, f"✅ {step.capitalize()} completed successfully")
                    elif 'failed' in queue_name:
                        step = queue_name.replace('_failed', '')
                        self.applications[app_id]['saga_status'] = 'failed'
                        error = data.get('error', 'Unknown error')
                        add_saga_log(app_id, step, f"❌ {step.capitalize()} failed: {error}")

                ch.basic_ack(delivery_tag=method.delivery_tag)
            except Exception as e:
                logger.error(f"Error handling event: {e}")
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

        # Subscribe to all event queues
        for queue in ['document_processed', 'document_failed', 'payment_processed',
                     'payment_failed', 'enrollment_processed', 'enrollment_failed',
                     'notification_sent', 'notification_failed']:
            self.consumer_channel.basic_consume(queue=queue, on_message_callback=handle_event)

        logger.info("📡 Event listener started")
        self.consumer_channel.start_consuming()

    def ensure_connection(self):
        """Check connection and reconnect if needed"""
        try:
            if self.connection is None or self.connection.is_closed:
                logger.warning("🔄 Reconnecting to RabbitMQ...")
                self.connect_rabbitmq()
                self.setup_queues()
        except Exception as e:
            logger.error(f"❌ Failed to ensure connection: {e}")

    def publish_event(self, queue: str, data: Dict[str, Any]):
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.ensure_connection()

                self.channel.basic_publish(
                    exchange='',
                    routing_key=queue,
                    body=json.dumps(data),
                    properties=pika.BasicProperties(delivery_mode=2)
                )
                logger.info(f"📤 Event sent to {queue} for {data['application_id']}")
                return True

            except Exception as e:
                logger.warning(f"⚠️ Publish attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(1)
                    try:
                        self.connect_rabbitmq()
                        self.setup_queues()
                    except:
                        pass
                else:
                    logger.error(f"❌ Failed to publish after {max_retries} attempts")
                    return False

    def generate_application_id(self) -> str:
        return f"APP-{int(time.time())%1000000}"

    def submit_application(self, request: ApplicationRequest) -> ApplicationResponse:
        application_id = self.generate_application_id()

        # Calculate application fee
        department_fees = {
            "Computer Science": 1500,
            "Mathematics": 1200,
            "Physics": 1300,
            "Biology": 1400,
            "Business": 1600
        }
        amount = department_fees.get(request.department, 1500)

        application_data = {
            'application_id': application_id,
            'student_name': request.student_name,
            'email': request.email,
            'department': request.department,
            'phone': request.phone,
            'amount': amount,
            'submitted_at': time.time(),
            'status': 'submitted',
            'saga_status': 'started'
        }
        self.applications[application_id] = application_data

        # Add initial log
        add_saga_log(application_id, "application_submitted",
                    f"Application received for {request.student_name} - {request.department}")

        # Publish event
        event_data = {
            'application_id': application_id,
            'student_name': request.student_name,
            'email': request.email,
            'department': request.department,
            'amount': amount
        }

        # Add force_fail if specified for demo
        if request.force_fail:
            event_data['force_fail'] = request.force_fail
            add_saga_log(application_id, "demo_mode", f"🎯 Demo: Force failure at {request.force_fail} step")

        success = self.publish_event('application_submitted', event_data)

        if success:
            update_status(application_id, "application_submitted")
            message = 'Application submitted successfully. Processing has started.'
            add_saga_log(application_id, "saga_started", "Saga orchestration initiated")
        else:
            application_data['saga_status'] = 'failed_to_start'
            message = 'Application submitted but processing may be delayed.'
            add_saga_log(application_id, "saga_failed", "Failed to start saga orchestration")

        return ApplicationResponse(
            application_id=application_id,
            status='submitted',
            message=message,
            timestamp=application_data['submitted_at']
        )

    def get_application_status(self, application_id: str):
        if application_id not in self.applications:
            raise HTTPException(status_code=404, detail="Application not found")
        return self.applications[application_id]

    def get_application_logs(self, application_id: str):
        if application_id not in self.applications:
            raise HTTPException(status_code=404, detail="Application not found")
        return saga_logs.get(application_id, [])

    def list_applications(self):
        return {
            "total": len(self.applications),
            "applications": list(self.applications.values())
        }

# Global service instance
service = ApplicationService()

@app.post("/applications", response_model=ApplicationResponse)
async def submit_application(request: ApplicationRequest):
    return service.submit_application(request)

@app.get("/applications/{application_id}")
async def get_status(application_id: str):
    return service.get_application_status(application_id)

@app.get("/applications/{application_id}/logs")
async def get_logs(application_id: str):
    """Get saga logs for an application"""
    logs = service.get_application_logs(application_id)
    return {
        "application_id": application_id,
        "logs": logs
    }

@app.get("/applications")
async def list_all():
    return service.list_applications()

@app.get("/health")
async def health_check():
    return {
        "status": "ok",
        "rabbitmq": "connected" if service.connection and not service.connection.is_closed else "disconnected",
        "applications_count": len(service.applications)
    }

# Startup event
@app.on_event("startup")
async def startup_event():
    logger.info("🚀 Application Service starting up...")

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    if service.connection and not service.connection.is_closed:
        service.connection.close()
    logger.info("🛑 Application Service shut down")
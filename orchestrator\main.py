import pika
import json
import time
from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any
import logging
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SagaState(Enum):
    STARTED = "started"
    DOCUMENT_PROCESSING = "document_processing"
    PAYMENT_PROCESSING = "payment_processing"
    ENROLLMENT_PROCESSING = "enrollment_processing"
    NOTIFICATION_PROCESSING = "notification_processing"
    COMPLETED = "completed"
    COMPENSATING = "compensating"
    COMPENSATION_COMPLETED = "compensation_completed"
    FAILED = "failed"

@dataclass
class SagaTransaction:
    application_id: str
    state: SagaState
    completed_steps: list
    compensated_steps: list = None
    failed_step: str = None
    error_message: str = None
    created_at: float = time.time()
    updated_at: float = None
    retry_count: int = 0
    max_retries: int = 3

    def __post_init__(self):
        if self.compensated_steps is None:
            self.compensated_steps = []
        if self.updated_at is None:
            self.updated_at = self.created_at

    def update_state(self, new_state: SagaState):
        """Update state with timestamp"""
        self.state = new_state
        self.updated_at = time.time()

    def can_retry(self) -> bool:
        """Check if transaction can be retried"""
        return self.retry_count < self.max_retries

    def increment_retry(self):
        """Increment retry counter"""
        self.retry_count += 1
        self.updated_at = time.time()

class SagaOrchestrator:
    def __init__(self):
        self.connection = None
        self.channel = None
        self.transactions: Dict[str, SagaTransaction] = {}
        self.connect_rabbitmq()
        self.setup_queues()

    def connect_rabbitmq(self):
        rabbitmq_host = os.getenv('RABBITMQ_HOST', 'localhost')
        rabbitmq_user = os.getenv('RABBITMQ_USER', 'admin')
        rabbitmq_pass = os.getenv('RABBITMQ_PASS', 'admin123')

        credentials = pika.PlainCredentials(rabbitmq_user, rabbitmq_pass)

        for i in range(10):
            try:
                self.connection = pika.BlockingConnection(
                    pika.ConnectionParameters(
                        host=rabbitmq_host,
                        credentials=credentials,
                        heartbeat=600,
                        blocked_connection_timeout=300
                    )
                )
                self.channel = self.connection.channel()
                logger.info(f"✅ {self.__class__.__name__} connected to RabbitMQ")
                return
            except pika.exceptions.AMQPConnectionError:
                logger.warning(f"⏳ Waiting for RabbitMQ... ({i+1}/10)")
                time.sleep(5)
        raise Exception("❌ Could not connect to RabbitMQ")

    def setup_queues(self):
        queues = [
            # Command queues
            'process_documents', 'compensate_documents',
            'process_payment', 'compensate_payment',
            'process_enrollment', 'compensate_enrollment',
            'send_notification', 'compensate_notification',
            # Event queues
            'document_processed', 'document_failed', 'documents_compensated',
            'payment_processed', 'payment_failed', 'payment_compensated',
            'enrollment_processed', 'enrollment_failed', 'enrollment_compensated',
            'notification_sent', 'notification_failed', 'notification_compensated',
            'application_submitted'
        ]
        for queue in queues:
            self.channel.queue_declare(queue=queue, durable=True)

    def publish_command(self, queue, data):
        self.channel.basic_publish(
            exchange='',
            routing_key=queue,
            body=json.dumps(data),
            properties=pika.BasicProperties(delivery_mode=2)
        )
        logger.info(f"📤 Command sent to {queue}: {data['application_id']}")

    def start_saga(self, application_id):
        transaction = SagaTransaction(
            application_id=application_id,
            state=SagaState.STARTED,
            completed_steps=[]
        )
        self.transactions[application_id] = transaction
        logger.info(f"🚀 Saga started for {application_id}")
        self.process_documents(application_id)

    def process_documents(self, application_id):
        self.transactions[application_id].state = SagaState.DOCUMENT_PROCESSING
        self.publish_command('process_documents', {
            'application_id': application_id,
            'saga_id': application_id
        })

    def process_payment(self, application_id):
        self.transactions[application_id].state = SagaState.PAYMENT_PROCESSING
        self.publish_command('process_payment', {
            'application_id': application_id,
            'saga_id': application_id
        })

    def process_enrollment(self, application_id):
        self.transactions[application_id].state = SagaState.ENROLLMENT_PROCESSING
        self.publish_command('process_enrollment', {
            'application_id': application_id,
            'saga_id': application_id
        })

    def send_notification(self, application_id):
        self.transactions[application_id].state = SagaState.NOTIFICATION_PROCESSING
        self.publish_command('send_notification', {
            'application_id': application_id,
            'saga_id': application_id,
            'message': 'Your application has been successfully processed!'
        })

    def handle_success(self, application_id, step):
        tx = self.transactions.get(application_id)
        if not tx:
            logger.warning(f"⚠️ No transaction found for {application_id}")
            return

        tx.completed_steps.append(step)
        tx.update_state(tx.state)  # Update timestamp
        logger.info(f"✅ Step completed: {step} for {application_id}")

        # Next step logic
        if step == 'documents':
            self.process_payment(application_id)
        elif step == 'payment':
            self.process_enrollment(application_id)
        elif step == 'enrollment':
            self.send_notification(application_id)
        elif step == 'notification':
            tx.update_state(SagaState.COMPLETED)
            logger.info(f"🎉 Saga completed successfully: {application_id}")

    def handle_failure(self, application_id, step, error):
        tx = self.transactions.get(application_id)
        if not tx:
            logger.warning(f"⚠️ No transaction found for {application_id}")
            return

        # Check if we can retry
        if tx.can_retry() and step != tx.failed_step:
            tx.increment_retry()
            logger.warning(f"🔄 Retrying step {step} for {application_id} (attempt {tx.retry_count}/{tx.max_retries})")

            # Retry the failed step
            if step == 'documents':
                self.process_documents(application_id)
            elif step == 'payment':
                self.process_payment(application_id)
            elif step == 'enrollment':
                self.process_enrollment(application_id)
            elif step == 'notification':
                self.send_notification(application_id)
            return

        # Max retries reached or same step failed again, start compensation
        tx.update_state(SagaState.COMPENSATING)
        tx.failed_step = step
        tx.error_message = error
        logger.error(f"❌ Step failed permanently: {step} for {application_id} - {error}")
        self.start_compensation(application_id)

    def start_compensation(self, application_id):
        tx = self.transactions.get(application_id)
        if not tx:
            return

        logger.info(f"🔄 Starting compensation for {application_id}")

        # Compensation mapping
        compensation_map = {
            'documents': 'compensate_documents',
            'payment': 'compensate_payment',
            'enrollment': 'compensate_enrollment',
            'notification': 'compensate_notification'
        }

        # Send compensation commands in reverse order
        for step in reversed(tx.completed_steps):
            if step in compensation_map:
                self.publish_command(compensation_map[step], {
                    'application_id': application_id,
                    'saga_id': application_id
                })
                logger.info(f"📤 Compensation command sent for {step}")

    def handle_compensation_completed(self, application_id, step):
        tx = self.transactions.get(application_id)
        if not tx:
            return

        tx.compensated_steps.append(step)
        logger.info(f"✅ Compensation completed for {step} - {application_id}")

        # Check if all compensations are completed
        if self.check_all_compensations_completed(application_id):
            tx.state = SagaState.COMPENSATION_COMPLETED
            logger.info(f"🔄 All compensations completed for {application_id}")

    def check_all_compensations_completed(self, application_id):
        tx = self.transactions.get(application_id)
        if not tx:
            return False

        # Check if all completed steps have been compensated
        return set(tx.completed_steps) == set(tx.compensated_steps)

    def get_saga_status(self, application_id):
        """Get detailed saga status"""
        tx = self.transactions.get(application_id)
        if not tx:
            return None

        return {
            'application_id': application_id,
            'state': tx.state.value,
            'completed_steps': tx.completed_steps,
            'compensated_steps': tx.compensated_steps,
            'failed_step': tx.failed_step,
            'error_message': tx.error_message,
            'retry_count': tx.retry_count,
            'max_retries': tx.max_retries,
            'created_at': tx.created_at,
            'updated_at': tx.updated_at,
            'duration': tx.updated_at - tx.created_at
        }

    def cleanup_old_transactions(self, max_age_hours=24):
        """Clean up old completed/failed transactions"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600

        to_remove = []
        for app_id, tx in self.transactions.items():
            if (tx.state in [SagaState.COMPLETED, SagaState.COMPENSATION_COMPLETED, SagaState.FAILED] and
                current_time - tx.updated_at > max_age_seconds):
                to_remove.append(app_id)

        for app_id in to_remove:
            del self.transactions[app_id]
            logger.info(f"🧹 Cleaned up old transaction: {app_id}")

        return len(to_remove)

    def on_event(self, queue, handler):
        self.channel.basic_consume(queue=queue, on_message_callback=handler, auto_ack=False)

    def start_consuming(self):
        # Application submission
        self.on_event('application_submitted', self.handle_app_submitted)

        # Success events
        self.on_event('document_processed', lambda ch, m, p, b: self._ack_success(ch, m, b, 'documents'))
        self.on_event('payment_processed', lambda ch, m, p, b: self._ack_success(ch, m, b, 'payment'))
        self.on_event('enrollment_processed', lambda ch, m, p, b: self._ack_success(ch, m, b, 'enrollment'))
        self.on_event('notification_sent', lambda ch, m, p, b: self._ack_success(ch, m, b, 'notification'))

        # Failure events
        self.on_event('document_failed', lambda ch, m, p, b: self._ack_fail(ch, m, b, 'documents'))
        self.on_event('payment_failed', lambda ch, m, p, b: self._ack_fail(ch, m, b, 'payment'))
        self.on_event('enrollment_failed', lambda ch, m, p, b: self._ack_fail(ch, m, b, 'enrollment'))
        self.on_event('notification_failed', lambda ch, m, p, b: self._ack_fail(ch, m, b, 'notification'))

        # Compensation completed events
        self.on_event('documents_compensated', lambda ch, m, p, b: self._ack_compensation(ch, m, b, 'documents'))
        self.on_event('payment_compensated', lambda ch, m, p, b: self._ack_compensation(ch, m, b, 'payment'))
        self.on_event('enrollment_compensated', lambda ch, m, p, b: self._ack_compensation(ch, m, b, 'enrollment'))
        self.on_event('notification_compensated', lambda ch, m, p, b: self._ack_compensation(ch, m, b, 'notification'))

        logger.info("🎯 Orchestrator listening...")
        self.channel.start_consuming()

    def _ack_success(self, ch, method, body, step):
        try:
            data = json.loads(body)
            self.handle_success(data['application_id'], step)
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            logger.error(f"Error in _ack_success: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

    def _ack_fail(self, ch, method, body, step):
        try:
            data = json.loads(body)
            self.handle_failure(data['application_id'], step, data.get('error', 'Unknown error'))
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            logger.error(f"Error in _ack_fail: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

    def _ack_compensation(self, ch, method, body, step):
        try:
            data = json.loads(body)
            self.handle_compensation_completed(data['application_id'], step)
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            logger.error(f"Error in _ack_compensation: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

    def handle_app_submitted(self, ch, method, properties, body):
        try:
            data = json.loads(body)
            self.start_saga(data['application_id'])
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            logger.error(f"Error in handle_app_submitted: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

if __name__ == "__main__":
    try:
        orchestrator = SagaOrchestrator()
        orchestrator.start_consuming()
    except KeyboardInterrupt:
        logger.info("🛑 Orchestrator stopped by user")
    except Exception as e:
        logger.error(f"❌ Orchestrator error: {e}")
narwhals-1.40.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
narwhals-1.40.0.dist-info/METADATA,sha256=Bin5oPMUvh2nRFEK4nnYG-TvHrSF1-utF5VEoancjHw,11104
narwhals-1.40.0.dist-info/RECORD,,
narwhals-1.40.0.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
narwhals-1.40.0.dist-info/licenses/LICENSE.md,sha256=heMD6hta6RzeBucppx59AUCgr_ukRY0ABj0bcrN3mKs,1071
narwhals/__init__.py,sha256=KHdu6YrCL9SkmEAbTfmzdD1K9Yl46ZYejDUozW3hbY0,4912
narwhals/__pycache__/__init__.cpython-312.pyc,,
narwhals/__pycache__/_duration.cpython-312.pyc,,
narwhals/__pycache__/_enum.cpython-312.pyc,,
narwhals/__pycache__/_expression_parsing.cpython-312.pyc,,
narwhals/__pycache__/_namespace.cpython-312.pyc,,
narwhals/__pycache__/_translate.cpython-312.pyc,,
narwhals/__pycache__/dataframe.cpython-312.pyc,,
narwhals/__pycache__/dependencies.cpython-312.pyc,,
narwhals/__pycache__/dtypes.cpython-312.pyc,,
narwhals/__pycache__/exceptions.cpython-312.pyc,,
narwhals/__pycache__/expr.cpython-312.pyc,,
narwhals/__pycache__/expr_cat.cpython-312.pyc,,
narwhals/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/__pycache__/expr_list.cpython-312.pyc,,
narwhals/__pycache__/expr_name.cpython-312.pyc,,
narwhals/__pycache__/expr_str.cpython-312.pyc,,
narwhals/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/__pycache__/functions.cpython-312.pyc,,
narwhals/__pycache__/group_by.cpython-312.pyc,,
narwhals/__pycache__/schema.cpython-312.pyc,,
narwhals/__pycache__/selectors.cpython-312.pyc,,
narwhals/__pycache__/series.cpython-312.pyc,,
narwhals/__pycache__/series_cat.cpython-312.pyc,,
narwhals/__pycache__/series_dt.cpython-312.pyc,,
narwhals/__pycache__/series_list.cpython-312.pyc,,
narwhals/__pycache__/series_str.cpython-312.pyc,,
narwhals/__pycache__/series_struct.cpython-312.pyc,,
narwhals/__pycache__/this.cpython-312.pyc,,
narwhals/__pycache__/translate.cpython-312.pyc,,
narwhals/__pycache__/typing.cpython-312.pyc,,
narwhals/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_arrow/__pycache__/__init__.cpython-312.pyc,,
narwhals/_arrow/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_arrow/__pycache__/expr.cpython-312.pyc,,
narwhals/_arrow/__pycache__/group_by.cpython-312.pyc,,
narwhals/_arrow/__pycache__/namespace.cpython-312.pyc,,
narwhals/_arrow/__pycache__/selectors.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_list.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_str.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_arrow/__pycache__/typing.cpython-312.pyc,,
narwhals/_arrow/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/dataframe.py,sha256=eZjs1Jax3l-DvVBRh3xqsnWCNcCHvp1FYJHbY8Mt8Cw,28929
narwhals/_arrow/expr.py,sha256=2tKtBK9LTnMzGkOruSvJMBM16CN7k39wYDNLn9sQEFg,8216
narwhals/_arrow/group_by.py,sha256=XW8Zkt57K9bU0BLF7kKWuSCciG14cUFNYihvrafm8Ts,6755
narwhals/_arrow/namespace.py,sha256=5hVGYj2vLLs-TM5W5hjcjTfAZKC_IUbRX7Vo4mlaTrE,11330
narwhals/_arrow/selectors.py,sha256=-h9N7KHUSMa1LuVtBDgaXWFrgw1konyYFqEKioXnuJw,1042
narwhals/_arrow/series.py,sha256=-8z5bXNhpF3KCLW_XzPwj4hca3CH9gDWnXP9eXPNGnQ,44559
narwhals/_arrow/series_cat.py,sha256=vvNlPaHHcA-ORzh_79-oY03wt6aIg1rLI0At8FXr2Ok,598
narwhals/_arrow/series_dt.py,sha256=49HWgA_AvkTGQI8v4ofWB2Oin5Lu6b3UkigRgk4HjU4,7863
narwhals/_arrow/series_list.py,sha256=EpSul8DmTjQW00NQ5nLn9ZBSSUR0uuZ0IK6TLX1utwI,421
narwhals/_arrow/series_str.py,sha256=1QW4AxAMaFGBVI7lquOiGZXdLbDISN9B2FA-mgKdKF4,2554
narwhals/_arrow/series_struct.py,sha256=85pQSUqOdeMyjsnjaSr_4YBC2HRGD-dsnNy2tPveJRM,410
narwhals/_arrow/typing.py,sha256=PIAb-ULA3t5Z8KAoQjUDSyb1V5FWO3djTlTMdveDzFc,2433
narwhals/_arrow/utils.py,sha256=ZjS1NXtjCq4I5rN8DIkX8XhlkIHuh6j4h2NkH_WSKSo,18034
narwhals/_compliant/__init__.py,sha256=D0E51BKhyi2q9kAabmHKRVdLAxMDgUHCZwFvT61fdzU,2745
narwhals/_compliant/__pycache__/__init__.cpython-312.pyc,,
narwhals/_compliant/__pycache__/any_namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_compliant/__pycache__/expr.cpython-312.pyc,,
narwhals/_compliant/__pycache__/group_by.cpython-312.pyc,,
narwhals/_compliant/__pycache__/namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/selectors.cpython-312.pyc,,
narwhals/_compliant/__pycache__/series.cpython-312.pyc,,
narwhals/_compliant/__pycache__/typing.cpython-312.pyc,,
narwhals/_compliant/__pycache__/when_then.cpython-312.pyc,,
narwhals/_compliant/any_namespace.py,sha256=Wxkg_Za2ycC__fkWx9QUB8i1ZEgts1v3LcEYWSReDaE,3327
narwhals/_compliant/dataframe.py,sha256=mUAdXw27RVTHqHf6kknii2cs3NphTD6WhHxTfeGeTZk,17787
narwhals/_compliant/expr.py,sha256=TE3T9LYPLWQ4HGf6YsgZT04PZdeCyQseaOip9ZkX3xA,40585
narwhals/_compliant/group_by.py,sha256=hI0PnjLZizqiMxLfOAFOCI4bv6QX8yMnYz491-Cpqlk,9205
narwhals/_compliant/namespace.py,sha256=hp11B1vYbMyaRBqHSBxG9C5OaUB0B8cA1Q7itYt5vcU,7993
narwhals/_compliant/selectors.py,sha256=ugPneQxSIj4rIC1k7k_0ZVl6Q8qTYOAUkaP4WAcCWhM,12859
narwhals/_compliant/series.py,sha256=tCEEAPSgUuN6mk1zencEpfjsd1HY2Ro4Sb08b9xaBbI,14461
narwhals/_compliant/typing.py,sha256=2pBH8PwNoby1usyE4UvHytgXGj0BpDClPXERSpOC-d0,6194
narwhals/_compliant/when_then.py,sha256=0khk0vrEfSa0tzWW8p0Rpd1Qu9StUHKM0iALgsl3J00,6323
narwhals/_dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_dask/__pycache__/__init__.cpython-312.pyc,,
narwhals/_dask/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_dask/__pycache__/group_by.cpython-312.pyc,,
narwhals/_dask/__pycache__/namespace.cpython-312.pyc,,
narwhals/_dask/__pycache__/selectors.cpython-312.pyc,,
narwhals/_dask/__pycache__/utils.cpython-312.pyc,,
narwhals/_dask/dataframe.py,sha256=JkEhT6WvqOI-qvQkZs-es9cN7sz1MUwifV6dg_CFrX8,16520
narwhals/_dask/expr.py,sha256=vPTrtgt5cccwPhc4TwbR1aIIpefjJikxtq-VKb3oxhA,25984
narwhals/_dask/expr_dt.py,sha256=Vz3InNLEN9RBW7WQF-Ogddli41jB5f_xNSX6TI2chjw,6568
narwhals/_dask/expr_str.py,sha256=rU-yY2oyUuIMl4BxeZZz0tpwoc7UGFIy_xlNwsIsjQA,3385
narwhals/_dask/group_by.py,sha256=4tKvwOr_H0gXvr-AuxK-lV7ppGW3jT3lab3_NgFhIYw,4355
narwhals/_dask/namespace.py,sha256=qyhMjMqpM2DHpF7cFD1MnN3jms2UiiM48o6yQ2AW9V0,12076
narwhals/_dask/selectors.py,sha256=37_48H9bl6kznhdqMw9ck7yvmmBi9ujaBGSn-8IA5ik,1015
narwhals/_dask/utils.py,sha256=-8Jh0DKVlD0kGIAarEtOIB1PqmzqhFbjizuGdF_0PBk,6805
narwhals/_duckdb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_duckdb/__pycache__/__init__.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/group_by.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/namespace.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/selectors.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/series.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/typing.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/utils.cpython-312.pyc,,
narwhals/_duckdb/dataframe.py,sha256=HdyHvqaaPmB02adKF8bd7VCD1IRqessPXBbTR7bBlnQ,19959
narwhals/_duckdb/expr.py,sha256=OnZpBoHMy5NA9voD3_BzCdAZdGto5xF7SPSzHmgRBqk,33407
narwhals/_duckdb/expr_dt.py,sha256=EjOeO3tMrZfQ1CbyEIo0_AgRaPeGtrL-yhDIYFBNXh4,4437
narwhals/_duckdb/expr_list.py,sha256=F1a7beLsKkve-53UugCrmdSWhextHSInoXMVkL6trKM,452
narwhals/_duckdb/expr_str.py,sha256=ZVbvqhgkL3WDXjI8BcaLp2BwINA94_8I-PSE1K72_Jc,3685
narwhals/_duckdb/expr_struct.py,sha256=2RhsCOEQ4pv5SDuxBi8b3Tw7MWPeueaZ5dKv98o__bU,541
narwhals/_duckdb/group_by.py,sha256=AxLvTDPg0tTMFGMdyN_kaB4bXofwRu-oUvrR-j-9LuA,1108
narwhals/_duckdb/namespace.py,sha256=uwplqFrzIalW5gS6R4vYqxFuAgfjhXnvohn9SmKn_ms,8618
narwhals/_duckdb/selectors.py,sha256=Yzp2sslSH3SSyIwGpBg9-PWxCbfTmdBDDHeklWG8OdE,967
narwhals/_duckdb/series.py,sha256=m6xT_DcrBO_FBa_RNR0DyoeyS1-8VO6cVPFU_GklN-s,1321
narwhals/_duckdb/typing.py,sha256=yHthS-6JRAclEhQ0PRfTFIpLrUMgT1jw69zJYGgD0tY,532
narwhals/_duckdb/utils.py,sha256=H5nNpna8kiLRvAj4aJO5zp2gr5-eFgBAqSUlj_Yr53k,9977
narwhals/_duration.py,sha256=AQo-sXAqbRe36l19AXX5arIg3GUeqJRjm0x10WDc3P8,2062
narwhals/_enum.py,sha256=sUR-04yIwjAMsX5eelKnc1UKXc5dBoj1do0krubAE04,1192
narwhals/_expression_parsing.py,sha256=VBZ9zvNojrGnsiOoX4EwoiFTgf2nvLKhxT_KH2Q_zPM,22289
narwhals/_ibis/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_ibis/__pycache__/__init__.cpython-312.pyc,,
narwhals/_ibis/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_ibis/__pycache__/group_by.cpython-312.pyc,,
narwhals/_ibis/__pycache__/namespace.cpython-312.pyc,,
narwhals/_ibis/__pycache__/selectors.cpython-312.pyc,,
narwhals/_ibis/__pycache__/series.cpython-312.pyc,,
narwhals/_ibis/__pycache__/typing.cpython-312.pyc,,
narwhals/_ibis/__pycache__/utils.cpython-312.pyc,,
narwhals/_ibis/dataframe.py,sha256=TZGL5cUweUmC-KosliZhIl8NJq-nuQR1MJ9W8VW0_TU,16506
narwhals/_ibis/expr.py,sha256=cJxbBrSPG2et_pTo0FHABkuDoa7qaV5KoLsm5O0ISQI,25555
narwhals/_ibis/expr_dt.py,sha256=AJhhieF4xcL4Q-EmyLvNa8j_Sb1iQopjxBYCM0TdITs,3487
narwhals/_ibis/expr_list.py,sha256=y560Cn6Y3LN67R_efppd3wmJY7pnYBywY_wp_z-lO_Y,363
narwhals/_ibis/expr_str.py,sha256=DOoYReWcWfnQc2dAbWBCTese-MqsAUW0mSaD_BnLTGE,3844
narwhals/_ibis/expr_struct.py,sha256=PSLlhaHTn-IU7ywebDGgqiMNBJhE6JBwxeK6BXqgj88,487
narwhals/_ibis/group_by.py,sha256=5sg0-PC2oSAPZblrZ5UOQxlPYcIvXeVVpPeZ1K2ZJFE,1016
narwhals/_ibis/namespace.py,sha256=eSss4dYvswq7686i4gZmDbWxYUr_xqh4NasMUrPTuxQ,8464
narwhals/_ibis/selectors.py,sha256=ecp672gQH6s6MvGFgxcT4sqzXp9q-qd7QV_Cy5-6IHc,932
narwhals/_ibis/series.py,sha256=54qFcXE7c2QwfMxwgYNdYiYvCih-vur6wqIy-va5EK0,1249
narwhals/_ibis/typing.py,sha256=fCWQ3CyFdVGgF3ytQ9iZMYHHfe7IWFJp2ff9dz48LUE,383
narwhals/_ibis/utils.py,sha256=6EniI8XdByoAEWSH9vb4c6juFweOUSZJVUPETmij_z0,8348
narwhals/_interchange/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_interchange/__pycache__/__init__.cpython-312.pyc,,
narwhals/_interchange/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_interchange/__pycache__/series.cpython-312.pyc,,
narwhals/_interchange/dataframe.py,sha256=OQKXuaaqd_U7toWSdOOxOITkGGGcfyLaFN57zcsNVbE,6550
narwhals/_interchange/series.py,sha256=jsULNMhw1ShCmw_h_BF8xDGa5b_HnOtqHVGsJ8AnUkI,1750
narwhals/_namespace.py,sha256=ThSi3eGpDWVQLO4hlPQF51ryMYVQjsKvx8xGchharVU,14151
narwhals/_pandas_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_pandas_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_list.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_str.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/typing.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_pandas_like/dataframe.py,sha256=tRxeh5xFkgGg-BL-pAWDi2WV6Dm6QQzUpKxVSyv3bn8,42591
narwhals/_pandas_like/expr.py,sha256=RFZyQ3FDwg2He63egdvmBq6zHlXh52osGoz4xWGJaM4,16593
narwhals/_pandas_like/group_by.py,sha256=K5yinHE0Xwb9-fcwe9hZhfHlghInpjd5lXbiDMI1Weo,13474
narwhals/_pandas_like/namespace.py,sha256=N9KMDhptLohDyKTiXcU5LHALBtVpNFfAGqjuk-1Q2eA,13146
narwhals/_pandas_like/selectors.py,sha256=5yjR5IfBdBwAvdav4Kay15wBwbrjagA8YDeqvMvEJMo,1175
narwhals/_pandas_like/series.py,sha256=5FBvBedNzwPQvQvjGn1mXZAJ-WXRZpYwSx0tUmZSkoE,40836
narwhals/_pandas_like/series_cat.py,sha256=MJwCnJ49hfnODh6JgMHOCQ2KBlTbmySU6_X4XWaqiz4,527
narwhals/_pandas_like/series_dt.py,sha256=UhIZAx9I7yXGwJMc_ctY_X2X3Ci4F8sfLqzHJ_858kw,9833
narwhals/_pandas_like/series_list.py,sha256=27VUWNEQridPz5_UoBjOhaIgma3ev3qFKtonoKwH6JI,1199
narwhals/_pandas_like/series_str.py,sha256=Wjn1C2XRgNgcujvk5f9AwEf4aJQ8clbA2MglTfGYbtA,3391
narwhals/_pandas_like/series_struct.py,sha256=vX9HoO42vHackvVozUfp8odM9uJ4owct49ydKDnohdk,518
narwhals/_pandas_like/typing.py,sha256=8L7LfKF777SuSgMARAB-VexDTGrvDUn4bioynyrwtCA,518
narwhals/_pandas_like/utils.py,sha256=KtEB_knIqtr4TwvXx_0SWgI2GaUCjaxlC4GEgwAm7Ic,26329
narwhals/_polars/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_polars/__pycache__/__init__.cpython-312.pyc,,
narwhals/_polars/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_polars/__pycache__/expr.cpython-312.pyc,,
narwhals/_polars/__pycache__/group_by.cpython-312.pyc,,
narwhals/_polars/__pycache__/namespace.cpython-312.pyc,,
narwhals/_polars/__pycache__/series.cpython-312.pyc,,
narwhals/_polars/__pycache__/typing.cpython-312.pyc,,
narwhals/_polars/__pycache__/utils.cpython-312.pyc,,
narwhals/_polars/dataframe.py,sha256=ldQ_n7SREsXRV2ziU9RnbhnsknCJe_uqL8EUY1wIyoI,26561
narwhals/_polars/expr.py,sha256=y8ShX_eqW09yHEwDIGBHdCRnNDduJr-6zz-A4oIq7C4,14576
narwhals/_polars/group_by.py,sha256=BbU3qhzTHIwjApnd0AQYaEHPRqrmnlz7OeaosXc_SGg,2554
narwhals/_polars/namespace.py,sha256=xb5S_sTqyeBWiistREXjPclcb2CUSE5gtZYF1fNi3uQ,11578
narwhals/_polars/series.py,sha256=yOHkHS3xDrW4Ir5rrTbG8q9ZyNOTPx1l-GNM2ocYCbY,26080
narwhals/_polars/typing.py,sha256=tsSQrfrC9k046fWQnPr3ACJOa7WUN8R8T-sNtrd56d4,702
narwhals/_polars/utils.py,sha256=chkx7WxYCKJ671_E2npwOTZzo-SLbdz2oCwoAVAI-Hk,8966
narwhals/_spark_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_spark_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/typing.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_spark_like/dataframe.py,sha256=4yavlxlBLPrEyzgKJsAny_ZXFwqqGWpon55mqJwrkG0,20544
narwhals/_spark_like/expr.py,sha256=8S_93HsmrJNSwjjS9STEMh1Ln_f9JMW_QgJtN_-yoxc,32780
narwhals/_spark_like/expr_dt.py,sha256=a03C74PoTLeYNHUKztWbtG3UWC2AbGIiYSpN8P17c4Q,3106
narwhals/_spark_like/expr_list.py,sha256=y3ZKxxoLz0_JAeGNi6ATSbrG5jeulThQmrk1AQP85mg,393
narwhals/_spark_like/expr_str.py,sha256=WLlgB8Ue2jaW40XX2EY9TKS33fcFYFlfF0SLgMi5HQY,5623
narwhals/_spark_like/expr_struct.py,sha256=07kjoKdGpkLuynJ-TPNgr6MmojBD-hAHF5hmkOb57fo,521
narwhals/_spark_like/group_by.py,sha256=FoJHx9rMmamrx0EnGBYTuUuvVL788ivcLZWutgbP9M0,1231
narwhals/_spark_like/namespace.py,sha256=26CZKnTKH-0stNjE4V1pbgxRrFFLDGVOUoAxWZJxUt0,10701
narwhals/_spark_like/selectors.py,sha256=fnj_AjxIBotnZ7SyyeVT_Zp9ayq0Zajb5Jm9aarWeu8,1049
narwhals/_spark_like/typing.py,sha256=pSSvViIEmJuzIjmKljgvD6yp3YnGLh5ImQ6ZJ4WMAFA,528
narwhals/_spark_like/utils.py,sha256=KhNJFAqg9TYPPHpWCNAdJgDqlA0jYb1W0O-zXbtyZik,8681
narwhals/_translate.py,sha256=4P_24mwArNIyvkKDmRED4aCUx4NN5o1MAM4mwOSzGnQ,6864
narwhals/dataframe.py,sha256=6ZuVgjMml2bECtR_ezIHEd1rXDC2dsAJHBwbH9G21Ms,127612
narwhals/dependencies.py,sha256=WTptPX2TPxx20bjpY6OT10eYwEEi1VORUPp8pEBCyeI,15818
narwhals/dtypes.py,sha256=3u1_te9V1nBUyxnYRIWR3l6kXZ6GkbQQAxWnUdyHRfY,23486
narwhals/exceptions.py,sha256=Vy44sE8jzL1fSLFGkSomMVcEPbgGkp_mQ29ECEQF-ZI,4328
narwhals/expr.py,sha256=qAMQFZUF2xTmdCGLAPdhtXBDdoAdGsee-aZjDjPSDls,104210
narwhals/expr_cat.py,sha256=tqLaWmFhPlJlki8YRW9aluf8PEmn0B1lkhSSSTNsbO0,1297
narwhals/expr_dt.py,sha256=YI9gIRATkjBz3FhCaQNPyzXgTPPxsh1ygPiL6nFE5H8,31458
narwhals/expr_list.py,sha256=HYtmfjA0KILRfbanRAFAn4blYEkSAj5WqaUpwTjWE6s,1808
narwhals/expr_name.py,sha256=iegSBJ5vDM-w7JG_NvckiGAVLCjr1tgoOLR_c3ObMFk,6066
narwhals/expr_str.py,sha256=lfmgkTdLFak9KDtFXVUECaiRBz8aJr6gUPQfZOI1UI8,17817
narwhals/expr_struct.py,sha256=ht2oJqSYi_obJJFUn4GYWTkXGwUELVBTWwMPOQpbmn4,1958
narwhals/functions.py,sha256=BajWQ6VBsYRI6aSG4GEWQ5FX7joD8NwHNqL8UuY4kWc,67424
narwhals/group_by.py,sha256=-I7M386N74phaAC050rmTF_md1CwZ2EE2k7V-RGKEGg,7359
narwhals/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/schema.py,sha256=jRQtVN0UMuOkXffeAwdQd7WLDzSxWd0pkrzsxYBiUig,6667
narwhals/selectors.py,sha256=ykjB0bQ_4ourYrP6gklh-FXnlo9Hy-0Tn_Pylsp_i6g,10918
narwhals/series.py,sha256=yhTLp2sBP9VVWa-Jt2Kp9hPd-MZtnEdOAU2HhyYDdKQ,88145
narwhals/series_cat.py,sha256=I5osb8Fj04iWqfEWjiyhVPiFYe3Kk_mTZXZjwn3jnRc,911
narwhals/series_dt.py,sha256=gYLSg3upeE9CfUOb6YorfNdAryhkC88cz3iMBj1V1xY,24256
narwhals/series_list.py,sha256=NznN1Z50RSGX4uQBO4OBMtu7YBHRM58tgPKoJjmOrDg,1041
narwhals/series_str.py,sha256=4a_nTdloK3PLfrVtYbfCAYmjMmnMCps9RSEloOIlSI8,14591
narwhals/series_struct.py,sha256=pxBH2ZHlQyU6egwcfdLXmGcRvBmM4MatVCkkMUFOarQ,1045
narwhals/stable/__init__.py,sha256=b9soCkGkQzgF5jO5EdQ6IOQpnc6G6eqWmY6WwpoSjhk,85
narwhals/stable/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__init__.py,sha256=uzjCLM6yOSBZyEDjlGKuwgL0Y2pI_UoeOLc7uMaJ_LM,62249
narwhals/stable/v1/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_namespace.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dependencies.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/selectors.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/typing.cpython-312.pyc,,
narwhals/stable/v1/_dtypes.py,sha256=-heA_lptD-9sTiH0j1UsXvERgj4MwS1dzS6BwyzB4js,3472
narwhals/stable/v1/_namespace.py,sha256=nsv44xpKnOO5E6IKKOJDuofqrif_IjRLEifBF25KfbM,295
narwhals/stable/v1/dependencies.py,sha256=PqYYOP_8w0ZJraajFnpYwc_ZdURRQIcgqplKsnymL_U,2204
narwhals/stable/v1/dtypes.py,sha256=C20rFSp455WJtGfaCZzxtUg600fPVZzIh9iAaz7bc40,2229
narwhals/stable/v1/selectors.py,sha256=4qDY5ZC6NyavSG6tK2M7kC1jOPSXJOltNzdgEx_Gu2Y,485
narwhals/stable/v1/typing.py,sha256=zKpjY_q2GR2tK-8TqsVwurHAfXTWmyBRoV097gssSxA,7065
narwhals/this.py,sha256=BbKcj0ReWqE01lznzKjuqq7otXONvjBevWWC5aJhQxs,1584
narwhals/translate.py,sha256=I54G3aZSQJ_dSQWHwXnioPQSsfd3mwXsYNnDBobOqNI,28693
narwhals/typing.py,sha256=GZDPWbQFTHJTOkEsgf4fUomjO8qSoaC6_VKJWzK2b80,14271
narwhals/utils.py,sha256=4pFs66MYoVAo372OkiYvaQuG8wqOQSLxPRIZpVhifo8,66117

import pika
import json
import time
import logging
import os
from utils import update_status

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentService:
    def __init__(self):
        self.connection = None
        self.channel = None
        self.connect_rabbitmq()
        self.setup_queues()

    def connect_rabbitmq(self):
        rabbitmq_host = os.getenv('RABBITMQ_HOST', 'localhost')
        rabbitmq_user = os.getenv('RABBITMQ_USER', 'admin')
        rabbitmq_pass = os.getenv('RABBITMQ_PASS', 'admin123')

        credentials = pika.PlainCredentials(rabbitmq_user, rabbitmq_pass)

        for i in range(10):
            try:
                self.connection = pika.BlockingConnection(
                    pika.ConnectionParameters(
                        host=rabbitmq_host,
                        credentials=credentials,
                        heartbeat=600,
                        blocked_connection_timeout=300
                    )
                )
                self.channel = self.connection.channel()
                logger.info(f"✅ {self.__class__.__name__} connected to RabbitMQ")
                return
            except pika.exceptions.AMQPConnectionError:
                logger.warning(f"⏳ Waiting for RabbitMQ... ({i+1}/10)")
                time.sleep(5)
        raise Exception("❌ Could not connect to RabbitMQ")

    def setup_queues(self):
        # Input queues
        self.channel.queue_declare(queue='process_documents', durable=True)
        self.channel.queue_declare(queue='compensate_documents', durable=True)

        # Output queues
        self.channel.queue_declare(queue='document_processed', durable=True)
        self.channel.queue_declare(queue='document_failed', durable=True)
        self.channel.queue_declare(queue='documents_compensated', durable=True)

        # Consumers
        self.channel.basic_consume(
            queue='process_documents',
            on_message_callback=self.process_document,
            auto_ack=False
        )

        self.channel.basic_consume(
            queue='compensate_documents',
            on_message_callback=self.compensate_document,
            auto_ack=False
        )

        logger.info("📄 Document Service is listening...")
        self.channel.start_consuming()

    def publish_event(self, queue, data):
        """Orchestrator'a event gönder"""
        self.channel.basic_publish(
            exchange='',
            routing_key=queue,
            body=json.dumps(data),
            properties=pika.BasicProperties(delivery_mode=2)
        )
        logger.info(f"📤 Event sent to {queue}: {data['application_id']}")

    def process_document(self, ch, method, properties, body):
        try:
            data = json.loads(body)
            application_id = data["application_id"]
            saga_id = data.get("saga_id", application_id)

            logger.info(f"📄 Processing documents for application {application_id}")

            # Belge işlemi simülasyonu
            time.sleep(2)

            # Check for demo force fail
            force_fail = data.get('force_fail')
            should_fail = False

            if force_fail == 'documents':
                should_fail = True
                error_msg = "🎯 Demo: Forced document failure"
            elif time.time() % 10 < 1:  # %10 hata simülasyonu
                should_fail = True
                error_msg = "Document validation failed"

            if should_fail:
                logger.error(f"❌ Document processing failed: {application_id}")
                self.publish_event('document_failed', {
                    'application_id': application_id,
                    'saga_id': saga_id,
                    'error': error_msg
                })
            else:
                update_status(application_id, "document_processed")
                logger.info(f"✅ Document processing completed for {application_id}")
                self.publish_event('document_processed', {
                    'application_id': application_id,
                    'saga_id': saga_id
                })

            ch.basic_ack(delivery_tag=method.delivery_tag)

        except Exception as e:
            logger.error(f"❌ Error processing document: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

    def compensate_document(self, ch, method, properties, body):
        try:
            data = json.loads(body)
            application_id = data["application_id"]
            saga_id = data.get("saga_id", application_id)

            logger.info(f"⏪ Compensating document for {application_id}")

            # Belge işlemini geri al simülasyonu
            time.sleep(1)

            # Status'u güncelle (document_processed'i false yap veya sil)
            status = update_status(application_id, "document_compensated")

            logger.info(f"✅ Document compensation completed for {application_id}")

            # Orchestrator'a bildir
            self.publish_event('documents_compensated', {
                'application_id': application_id,
                'saga_id': saga_id
            })

            ch.basic_ack(delivery_tag=method.delivery_tag)

        except Exception as e:
            logger.error(f"❌ Error compensating document: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

if __name__ == "__main__":
    DocumentService()
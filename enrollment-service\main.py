import pika
import json
import time
import logging
import os
from utils import update_status

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnrollmentService:
    def __init__(self):
        self.connection = None
        self.channel = None
        self.enrollments = {}  # application_id -> enrollment_info
        self.connect_rabbitmq()
        self.setup_queues()

    def connect_rabbitmq(self):
        rabbitmq_host = os.getenv('RABBITMQ_HOST', 'localhost')
        rabbitmq_user = os.getenv('RABBITMQ_USER', 'admin')
        rabbitmq_pass = os.getenv('RABBITMQ_PASS', 'admin123')

        credentials = pika.PlainCredentials(rabbitmq_user, rabbitmq_pass)

        for i in range(10):
            try:
                self.connection = pika.BlockingConnection(
                    pika.ConnectionParameters(
                        host=rabbitmq_host,
                        credentials=credentials,
                        heartbeat=600,
                        blocked_connection_timeout=300
                    )
                )
                self.channel = self.connection.channel()
                logger.info("✅ Enrollment Service connected to RabbitMQ")
                return
            except pika.exceptions.AMQPConnectionError:
                logger.warning(f"⏳ Waiting for RabbitMQ... ({i+1}/10)")
                time.sleep(5)
        raise Exception("❌ Could not connect to RabbitMQ")

    def setup_queues(self):
        # Input queues
        self.channel.queue_declare(queue='process_enrollment', durable=True)
        self.channel.queue_declare(queue='compensate_enrollment', durable=True)

        # Output queues
        self.channel.queue_declare(queue='enrollment_processed', durable=True)
        self.channel.queue_declare(queue='enrollment_failed', durable=True)
        self.channel.queue_declare(queue='enrollment_compensated', durable=True)

        # Consumers
        self.channel.basic_consume(
            queue='process_enrollment',
            on_message_callback=self.process_enrollment,
            auto_ack=False
        )

        self.channel.basic_consume(
            queue='compensate_enrollment',
            on_message_callback=self.compensate_enrollment,
            auto_ack=False
        )

        logger.info("🎓 Enrollment Service is listening...")
        self.channel.start_consuming()

    def publish_event(self, queue, data):
        self.channel.basic_publish(
            exchange='',
            routing_key=queue,
            body=json.dumps(data),
            properties=pika.BasicProperties(delivery_mode=2)
        )
        logger.info(f"📤 Event sent to {queue}: {data['application_id']}")

    def process_enrollment(self, ch, method, properties, body):
        try:
            data = json.loads(body)
            application_id = data["application_id"]
            saga_id = data.get("saga_id", application_id)

            logger.info(f"🎓 Processing enrollment for application {application_id}")

            # Kayıt işlemi simülasyonu
            time.sleep(2)

            # Öğrenci numarası oluştur
            student_id = f"STU-{int(time.time()) % 1000000}"

            # Check for demo force fail
            force_fail = data.get('force_fail')
            should_fail = False

            if force_fail == 'enrollment':
                should_fail = True
                error_msg = "🎯 Demo: Forced enrollment failure"
            elif time.time() % 20 < 1:  # %5 hata simülasyonu (nadiren hata)
                should_fail = True
                error_msg = "Student quota exceeded for department"

            if should_fail:
                logger.error(f"❌ Enrollment failed: {application_id}")
                self.publish_event('enrollment_failed', {
                    'application_id': application_id,
                    'saga_id': saga_id,
                    'error': error_msg
                })
            else:
                # Kayıt bilgilerini sakla
                self.enrollments[application_id] = {
                    'student_id': student_id,
                    'enrolled_at': time.time(),
                    'status': 'active',
                    'semester': 'Fall 2024'
                }

                # Status güncelle
                update_status(application_id, "enrollment_processed")

                logger.info(f"✅ Enrollment completed for {application_id} - Student ID: {student_id}")
                self.publish_event('enrollment_processed', {
                    'application_id': application_id,
                    'saga_id': saga_id,
                    'student_id': student_id
                })

            ch.basic_ack(delivery_tag=method.delivery_tag)

        except Exception as e:
            logger.error(f"❌ Error processing enrollment: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

    def compensate_enrollment(self, ch, method, properties, body):
        try:
            data = json.loads(body)
            application_id = data["application_id"]
            saga_id = data.get("saga_id", application_id)

            logger.info(f"🎓 Cancelling enrollment for {application_id}")

            # Kayıt bilgilerini kontrol et
            if application_id in self.enrollments:
                enrollment_info = self.enrollments[application_id]
                student_id = enrollment_info['student_id']

                # Kayıt iptali simülasyonu
                time.sleep(1)

                # Kayıt bilgilerini güncelle
                enrollment_info['status'] = 'cancelled'
                enrollment_info['cancelled_at'] = time.time()
                enrollment_info['cancellation_reason'] = 'Saga compensation'

                logger.info(f"✅ Enrollment cancelled for {application_id} - Student ID: {student_id}")

                # Status güncelle
                update_status(application_id, "enrollment_cancelled")
            else:
                logger.warning(f"⚠️ No enrollment found to cancel for {application_id}")

            # Orchestrator'a bildir
            self.publish_event('enrollment_compensated', {
                'application_id': application_id,
                'saga_id': saga_id
            })

            ch.basic_ack(delivery_tag=method.delivery_tag)

        except Exception as e:
            logger.error(f"❌ Error compensating enrollment: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

if __name__ == "__main__":
    EnrollmentService()
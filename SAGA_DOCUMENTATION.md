# University Application Saga Pattern Implementation

## Overview

This project implements the **Saga Pattern** for managing distributed transactions in a university application system. The saga ensures data consistency across multiple microservices while handling failures gracefully through compensation actions.

## Architecture

### Services
- **Application Service**: Entry point for student applications
- **Document Service**: Processes and validates documents
- **Payment Service**: Handles application fee payments
- **Enrollment Service**: Manages student enrollment
- **Notification Service**: Sends confirmation notifications
- **Orchestrator**: Central coordinator managing saga flow

### Message Broker
- **RabbitMQ**: Handles asynchronous communication between services

## Saga Flow

### Happy Path
1. **Application Submitted** → Document Processing
2. **Documents Processed** → Payment Processing  
3. **Payment Processed** → Enrollment Processing
4. **Enrollment Processed** → Notification Sending
5. **Notification Sent** → Saga Completed ✅

### Failure Path with Compensation
1. **Application Submitted** → Document Processing ✅
2. **Documents Processed** → Payment Processing ❌
3. **Payment Failed** → Start Compensation
4. **Compensate Documents** → Rollback completed steps
5. **Compensation Completed** → Saga Failed but Consistent ✅

## Key Features

### 1. State Management
```python
class SagaState(Enum):
    STARTED = "started"
    DOCUMENT_PROCESSING = "document_processing"
    PAYMENT_PROCESSING = "payment_processing"
    ENROLLMENT_PROCESSING = "enrollment_processing"
    NOTIFICATION_PROCESSING = "notification_processing"
    COMPLETED = "completed"
    COMPENSATING = "compensating"
    COMPENSATION_COMPLETED = "compensation_completed"
    FAILED = "failed"
```

### 2. Retry Logic
- Automatic retry for failed steps (up to 3 attempts)
- Exponential backoff between retries
- Compensation only after max retries exceeded

### 3. Compensation Mapping
```python
compensation_map = {
    'documents': 'compensate_documents',
    'payment': 'compensate_payment',
    'enrollment': 'compensate_enrollment',
    'notification': 'compensate_notification'
}
```

### 4. Transaction Tracking
- Detailed transaction state with timestamps
- Completed and compensated steps tracking
- Error messages and retry counts
- Duration tracking for performance monitoring

## Running the System

### 1. Start Services
```bash
docker-compose up -d
```

### 2. Test the Saga
```bash
python test_saga.py
```

### 3. Monitor Logs
```bash
docker-compose logs -f orchestrator
docker-compose logs -f payment-service
```

## API Endpoints

### Application Service (Port 8000)
- `POST /applications` - Submit new application
- `GET /applications/{id}` - Get application status
- `GET /applications/{id}/logs` - Get saga logs
- `GET /applications` - List all applications
- `GET /health` - Health check

### UI Service (Port 8501)
- Streamlit web interface for testing

## Testing Scenarios

### 1. Successful Application
```python
student_data = {
    "student_name": "Alice Johnson",
    "email": "<EMAIL>", 
    "department": "Computer Science",
    "phone": "**********"
}
```

### 2. Payment Failure (15% chance)
The payment service simulates random failures to test compensation logic.

### 3. Multiple Applications
Test concurrent saga execution with different outcomes.

## Monitoring and Observability

### 1. Saga Logs
Each step is logged with timestamps and status:
```json
{
  "timestamp": **********.789,
  "step": "payment",
  "message": "✅ Payment completed successfully"
}
```

### 2. Transaction Status
Detailed saga state tracking:
```json
{
  "application_id": "APP-123456",
  "state": "completed",
  "completed_steps": ["documents", "payment", "enrollment", "notification"],
  "compensated_steps": [],
  "retry_count": 0,
  "duration": 12.5
}
```

### 3. Health Monitoring
- RabbitMQ connection status
- Service availability
- Active transaction count

## Best Practices Implemented

### 1. Idempotency
- All operations are idempotent
- Duplicate message handling
- Safe retry mechanisms

### 2. Error Handling
- Graceful failure handling
- Detailed error logging
- Proper message acknowledgment

### 3. Scalability
- Stateless services
- Horizontal scaling support
- Load balancing ready

### 4. Reliability
- Persistent message queues
- Transaction durability
- Automatic recovery

## Configuration

### Environment Variables
```bash
RABBITMQ_HOST=localhost
RABBITMQ_USER=admin
RABBITMQ_PASS=admin123
```

### Service Ports
- Application Service: 8000
- UI Service: 8501
- RabbitMQ: 5672 (AMQP), 15672 (Management)

## Troubleshooting

### Common Issues

1. **RabbitMQ Connection Failed**
   - Check if RabbitMQ is running
   - Verify credentials
   - Check network connectivity

2. **Saga Stuck in Processing**
   - Check service logs
   - Verify message queue status
   - Restart orchestrator if needed

3. **Compensation Not Working**
   - Verify compensation handlers
   - Check message routing
   - Review error logs

### Debug Commands
```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs [service-name]

# Restart specific service
docker-compose restart [service-name]

# Access RabbitMQ Management
http://localhost:15672 (admin/admin123)
```

## Performance Considerations

### 1. Message Processing
- Async processing for better throughput
- Configurable retry delays
- Dead letter queues for failed messages

### 2. Memory Management
- Automatic cleanup of old transactions
- Configurable retention periods
- Memory-efficient data structures

### 3. Network Optimization
- Connection pooling
- Heartbeat configuration
- Timeout management

## Security Considerations

### 1. Message Security
- Encrypted message payloads (can be added)
- Secure RabbitMQ credentials
- Network isolation

### 2. Data Protection
- PII data handling
- Audit logging
- Access control

## Future Enhancements

1. **Persistence Layer**
   - Database storage for saga state
   - Event sourcing implementation

2. **Advanced Monitoring**
   - Metrics collection
   - Alerting system
   - Performance dashboards

3. **Circuit Breaker**
   - Failure detection
   - Automatic service isolation
   - Recovery mechanisms

4. **Distributed Tracing**
   - Request correlation
   - End-to-end visibility
   - Performance analysis

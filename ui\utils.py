import json
import os
import logging
import time
# Shared volume path
STATUS_FILE = "/app/shared/status.json"

logger = logging.getLogger(__name__)

def load_status():
    try:
        if os.path.exists(STATUS_FILE):
            with open(STATUS_FILE, "r") as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"Error loading status: {e}")
    return {}

def save_status(status):
    try:
        # Shared dizini olu<PERSON>tur
        os.makedirs(os.path.dirname(STATUS_FILE), exist_ok=True)
        with open(STATUS_FILE, "w") as f:
            json.dump(status, f, indent=4)
    except Exception as e:
        logger.error(f"Error saving status: {e}")

def update_status(app_id, step):
    status = load_status()
    if app_id not in status:
        status[app_id] = {}
    status[app_id][step] = True
    status[app_id]["last_updated"] = time.time()
    save_status(status)
    logger.info(f"Status updated for {app_id}: {step}")
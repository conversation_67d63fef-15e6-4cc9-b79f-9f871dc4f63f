import pika
import json
import time
import logging
import os
from typing import Dict, Any
from utils import update_status

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NotificationService:
    def __init__(self):
        self.connection = None
        self.channel = None
        self.notifications: Dict[str, Dict] = {}
        self.connect_rabbitmq()
        self.setup_queues()

    def connect_rabbitmq(self):
        rabbitmq_host = os.getenv('RABBITMQ_HOST', 'localhost')
        rabbitmq_user = os.getenv('RABBITMQ_USER', 'admin')
        rabbitmq_pass = os.getenv('RABBITMQ_PASS', 'admin123')

        credentials = pika.PlainCredentials(rabbitmq_user, rabbitmq_pass)

        for i in range(10):
            try:
                self.connection = pika.BlockingConnection(
                    pika.ConnectionParameters(
                        host=rabbitmq_host,
                        credentials=credentials,
                        heartbeat=600,
                        blocked_connection_timeout=300
                    )
                )
                self.channel = self.connection.channel()
                logger.info("✅ Notification Service connected to RabbitMQ")
                return
            except pika.exceptions.AMQPConnectionError:
                logger.warning(f"⏳ Waiting for RabbitMQ... ({i+1}/10)")
                time.sleep(5)
        raise Exception("❌ Could not connect to RabbitMQ")

    def setup_queues(self):
        # Input queues
        self.channel.queue_declare(queue='send_notification', durable=True)
        self.channel.queue_declare(queue='compensate_notification', durable=True)

        # Output queues
        self.channel.queue_declare(queue='notification_sent', durable=True)
        self.channel.queue_declare(queue='notification_failed', durable=True)
        self.channel.queue_declare(queue='notification_compensated', durable=True)

        # Consumers
        self.channel.basic_qos(prefetch_count=1)

        self.channel.basic_consume(
            queue='send_notification',
            on_message_callback=self.send_notification
        )

        self.channel.basic_consume(
            queue='compensate_notification',
            on_message_callback=self.compensate_notification
        )

        logger.info("📧 Notification Service is listening...")
        self.channel.start_consuming()

    def publish_event(self, queue: str, data: Dict[str, Any]):
        self.channel.basic_publish(
            exchange='',
            routing_key=queue,
            body=json.dumps(data),
            properties=pika.BasicProperties(delivery_mode=2)
        )
        logger.info(f"📤 Event published to {queue}: {data['application_id']}")

    def send_notification(self, ch, method, properties, body):
        try:
            data = json.loads(body)
            application_id = data['application_id']
            saga_id = data.get('saga_id', application_id)
            message = data.get('message', 'Your application has been processed successfully!')

            logger.info(f"📧 Sending notification for {application_id}")

            # Notification gönderme simülasyonu
            time.sleep(1)

            # Check for demo force fail
            force_fail = data.get('force_fail')
            should_fail = False

            if force_fail == 'notification':
                should_fail = True
                error_msg = "🎯 Demo: Forced notification failure"
            elif time.time() % 5 < 1:  # %20 hata simülasyonu
                should_fail = True
                error_msg = "SMTP service timeout - Unable to send email"

            if should_fail:
                logger.error(f"❌ Notification failed: {application_id} - {error_msg}")
                self.publish_event('notification_failed', {
                    'application_id': application_id,
                    'saga_id': saga_id,
                    'error': error_msg
                })
            else:
                # Notification bilgilerini sakla
                self.notifications[application_id] = {
                    'sent_at': time.time(),
                    'status': 'sent',
                    'message': message,
                    'notification_id': f"NOTIF-{int(time.time())}"
                }

                # Status güncelle
                update_status(application_id, "notification_sent")

                logger.info(f"✅ Notification sent: {application_id}")
                self.publish_event('notification_sent', {
                    'application_id': application_id,
                    'saga_id': saga_id
                })

            ch.basic_ack(delivery_tag=method.delivery_tag)

        except Exception as e:
            logger.error(f"❌ Error in send_notification: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

    def compensate_notification(self, ch, method, properties, body):
        try:
            data = json.loads(body)
            application_id = data['application_id']
            saga_id = data.get('saga_id', application_id)

            logger.info(f"⏪ Compensating notification for {application_id}")

            # Notification bilgilerini kontrol et
            if application_id in self.notifications:
                notification_info = self.notifications[application_id]

                # Notification iptali simülasyonu (örn: iptal maili gönderme)
                time.sleep(0.5)

                # Bilgileri güncelle
                notification_info['status'] = 'compensated'
                notification_info['compensated_at'] = time.time()
                notification_info['compensation_message'] = 'Application cancelled - Please disregard previous notification'

                logger.info(f"✅ Notification compensated for {application_id}")

                # Status güncelle
                update_status(application_id, "notification_compensated")
            else:
                logger.warning(f"⚠️ No notification found to compensate for {application_id}")

            # Orchestrator'a bildir
            self.publish_event('notification_compensated', {
                'application_id': application_id,
                'saga_id': saga_id
            })

            ch.basic_ack(delivery_tag=method.delivery_tag)

        except Exception as e:
            logger.error(f"❌ Error in compensate_notification: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

if __name__ == "__main__":
    NotificationService()
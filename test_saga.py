import requests
import time
import json

# Test application gönder
def test_saga():
    # 1. Application gönder
    response = requests.post("http://localhost:8000/applications", json={
        "student_name": "Test Student",
        "email": "<EMAIL>",
        "department": "Computer Science",
        "phone": "1234567890"
    })
    
    print("Application Response:", response.json())
    app_id = response.json()["application_id"]
    
    # 2. Status kontrolü
    for i in range(10):
        time.sleep(2)
        status = requests.get(f"http://localhost:8000/applications/{app_id}")
        print(f"\nStatus Check {i+1}:", status.json())
        
        # Logs kontrolü
        logs = requests.get(f"http://localhost:8000/applications/{app_id}/logs")
        if logs.status_code == 200:
            print("Logs:", json.dumps(logs.json(), indent=2))
    
    # 3. Tüm application'ları listele
    all_apps = requests.get("http://localhost:8000/applications")
    print("\nAll Applications:", json.dumps(all_apps.json(), indent=2))

if __name__ == "__main__":
    test_saga()
import requests
import time
import json
from typing import Dict, Any

class SagaTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url

    def submit_application(self, student_data: Dict[str, Any]) -> str:
        """Submit a new application and return application ID"""
        response = requests.post(f"{self.base_url}/applications", json=student_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Application submitted: {result['application_id']}")
            return result["application_id"]
        else:
            print(f"❌ Failed to submit application: {response.text}")
            return None

    def monitor_saga(self, app_id: str, max_checks=15, check_interval=2):
        """Monitor saga progress with detailed logging"""
        print(f"\n🔍 Monitoring saga for application: {app_id}")
        print("=" * 60)

        for i in range(max_checks):
            try:
                # Get application status
                status_response = requests.get(f"{self.base_url}/applications/{app_id}")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    saga_status = status_data.get('saga_status', 'unknown')

                    print(f"\n📊 Check {i+1}/{max_checks} - Saga Status: {saga_status}")

                    # Get detailed logs
                    logs_response = requests.get(f"{self.base_url}/applications/{app_id}/logs")
                    if logs_response.status_code == 200:
                        logs_data = logs_response.json()
                        logs = logs_data.get('logs', [])

                        if logs:
                            print("📝 Recent logs:")
                            for log in logs[-3:]:  # Show last 3 logs
                                timestamp = time.strftime('%H:%M:%S', time.localtime(log['timestamp']))
                                print(f"   {timestamp} | {log['step']}: {log['message']}")

                    # Check if saga is completed or failed
                    if saga_status in ['completed', 'failed', 'compensation_completed']:
                        print(f"\n🏁 Saga finished with status: {saga_status}")
                        return status_data

                else:
                    print(f"❌ Failed to get status: {status_response.text}")

            except Exception as e:
                print(f"❌ Error monitoring saga: {e}")

            time.sleep(check_interval)

        print(f"\n⏰ Monitoring timeout after {max_checks} checks")
        return None

    def test_successful_saga(self):
        """Test a saga that should complete successfully"""
        print("\n🧪 Testing Successful Saga")
        print("=" * 50)

        student_data = {
            "student_name": "Alice Johnson",
            "email": "<EMAIL>",
            "department": "Computer Science",
            "phone": "1234567890"
        }

        app_id = self.submit_application(student_data)
        if app_id:
            return self.monitor_saga(app_id)
        return None

    def test_multiple_applications(self, count=3):
        """Test multiple applications to see different outcomes"""
        print(f"\n🧪 Testing {count} Applications")
        print("=" * 50)

        applications = []
        departments = ["Computer Science", "Mathematics", "Physics", "Biology", "Business"]

        for i in range(count):
            student_data = {
                "student_name": f"Student {i+1}",
                "email": f"student{i+1}@example.com",
                "department": departments[i % len(departments)],
                "phone": f"123456789{i}"
            }

            app_id = self.submit_application(student_data)
            if app_id:
                applications.append(app_id)
                time.sleep(1)  # Small delay between submissions

        # Monitor all applications
        for app_id in applications:
            print(f"\n--- Monitoring {app_id} ---")
            self.monitor_saga(app_id, max_checks=10, check_interval=1)

        return applications

    def get_all_applications(self):
        """Get summary of all applications"""
        try:
            response = requests.get(f"{self.base_url}/applications")
            if response.status_code == 200:
                data = response.json()
                print(f"\n📋 Total Applications: {data['total']}")

                for app in data['applications']:
                    status = app.get('saga_status', 'unknown')
                    name = app.get('student_name', 'Unknown')
                    app_id = app.get('application_id', 'Unknown')
                    print(f"   {app_id}: {name} - {status}")

                return data
            else:
                print(f"❌ Failed to get applications: {response.text}")
        except Exception as e:
            print(f"❌ Error getting applications: {e}")
        return None

    def health_check(self):
        """Check if the application service is healthy"""
        try:
            response = requests.get(f"{self.base_url}/health")
            if response.status_code == 200:
                health_data = response.json()
                print("🏥 Health Check:")
                print(f"   Status: {health_data.get('status', 'unknown')}")
                print(f"   RabbitMQ: {health_data.get('rabbitmq', 'unknown')}")
                print(f"   Applications: {health_data.get('applications_count', 0)}")
                return True
            else:
                print(f"❌ Health check failed: {response.text}")
        except Exception as e:
            print(f"❌ Health check error: {e}")
        return False

def main():
    """Main test function"""
    tester = SagaTester()

    print("🚀 University Saga Pattern Test Suite")
    print("=" * 60)

    # Health check first
    if not tester.health_check():
        print("❌ Service is not healthy. Please check if the application is running.")
        return

    print("\n" + "="*60)

    # Test single successful application
    tester.test_successful_saga()

    print("\n" + "="*60)

    # Test multiple applications
    tester.test_multiple_applications(3)

    print("\n" + "="*60)

    # Final summary
    tester.get_all_applications()

    print("\n🎯 Test completed! Check the logs above for saga execution details.")

if __name__ == "__main__":
    main()